from typing import Any, Dict, Optional, Union

from pydantic import AnyUrl, PostgresDsn, field_validator, model_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Application settings
    ENV: str = "dev"
    APP_NAME: str = "api-gateway"
    DEBUG: bool = False
    API_V1_STR: str = "/api/v1"

    # Kafka settings
    KAFKA_BROKER_HOST: str = "localhost"
    KAFKA_BROKER_PORT: str = "9092"

    # Rate limiting
    RATE_LIMIT_REQUESTS: int = 100
    RATE_LIMIT_PERIOD: int = 60  # seconds

    # CORS settings
    CORS_ORIGINS: Union[list[str], str] = [
        "http://localhost",
        "http://localhost:3000",  # React default port
        "http://localhost:8000",  # API Gateway itself
    ]
    CORS_CREDENTIALS: bool = True
    CORS_METHODS: Union[list[str], str] = ["*"]
    CORS_HEADERS: Union[list[str], str] = ["*"]

    # Service discovery settings
    SERVICE_DISCOVERY_ENABLED: bool = False
    SERVICE_DISCOVERY_HOST: str = "consul"
    SERVICE_DISCOVERY_PORT: int = 8500

    # JWT settings
    JWT_SECRET_KEY: str = "your-secret-key-at-least-32-chars-long"
    JWT_ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    REFRESH_TOKEN_EXPIRE_DAYS: int = 7

    # Redis settings
    REDIS_HOST: str = ""
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: str = ""
    REDIS_JWT_ACCESS_EXPIRE_SEC: int = 3600
    REDIS_URI: Optional[str] = None  # Add this line

    # Server Authentication
    SERVER_AUTH_KEY: str = ""

    @field_validator("CORS_ORIGINS", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [origin.strip() for origin in v.split(',') if origin.strip()]
        return v

    @field_validator("CORS_METHODS", mode="before")
    @classmethod
    def parse_cors_methods(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [method.strip() for method in v.split(',') if method.strip()]
        return v

    @field_validator("CORS_HEADERS", mode="before")
    @classmethod
    def parse_cors_headers(cls, v: Union[str, list[str]]) -> list[str]:
        if isinstance(v, str):
            # Handle wildcard
            if v.strip() == "*":
                return ["*"]
            # Handle comma-separated string
            if v.startswith('[') and v.endswith(']'):
                # It's already a JSON-like string, let pydantic handle it
                import json
                return json.loads(v)
            else:
                # Split by comma and strip whitespace
                return [header.strip() for header in v.split(',') if header.strip()]
        return v

    @model_validator(mode="after")
    def assemble_redis_connection(self) -> "Settings":
        if self.REDIS_URI is not None:
            return self

        # Build Redis URI from individual components
        if not self.REDIS_HOST:
            # If no Redis host is provided, set a default URI
            self.REDIS_URI = "redis://localhost:6379/0"
            return self

        auth_part = f":{self.REDIS_PASSWORD}@" if self.REDIS_PASSWORD else ""
        self.REDIS_URI = f"redis://{auth_part}{self.REDIS_HOST}:{self.REDIS_PORT}/{self.REDIS_DB}"
        return self

    class Config:
        env_file = ".env"
        case_sensitive = True


settings = Settings()
