#!/usr/bin/env python3

import os
import sys

# Test the CORS_ORIGINS parsing
os.environ['CORS_ORIGINS'] = 'http://localhost,http://localhost:3000,http://localhost:8000,https://ruh-workflow.rapidinnovation.dev,https://ruh-marketplace.rapidinnovation.dev,https://ruh.rapidinnovation.dev'
os.environ['CORS_CREDENTIALS'] = 'true'
os.environ['CORS_METHODS'] = '*'
os.environ['CORS_HEADERS'] = '*'

try:
    from app.core.config import Settings
    settings = Settings()
    print("SUCCESS!")
    print(f"CORS_ORIGINS: {settings.CORS_ORIGINS}")
    print(f"CORS_METHODS: {settings.CORS_METHODS}")
    print(f"CORS_HEADERS: {settings.CORS_HEADERS}")
except Exception as e:
    print(f"ERROR: {e}")
    import traceback
    traceback.print_exc()
