"""
Direct SSH streams for MCP communication.
Bypasses the problematic MCP SDK on Windows.
"""

import asyncio
import json
import logging
from typing import Any, Dict, Optional
import subprocess


class DirectSSHReadStream:
    """Read stream for direct SSH MCP communication."""

    def __init__(self, process, logger: logging.Logger):
        self.process = process  # asyncio subprocess
        self.logger = logger
        self._buffer = ""

    async def read(self) -> Optional[Dict[str, Any]]:
        """Read a JSON-RPC message from the SSH process."""
        try:
            # Use asyncio subprocess readline (much better than run_in_executor)
            if self.process.stdout:
                try:
                    # Use a longer timeout for MCP responses to prevent premature timeouts
                    line_bytes = await asyncio.wait_for(
                        self.process.stdout.readline(),
                        timeout=10.0,
                    )

                    if line_bytes:
                        line = line_bytes.decode().strip()
                        self.logger.info(f"Read from SSH: {line}")

                        if line:
                            try:
                                message = json.loads(line)
                                self.logger.info(f"Parsed JSON message: {message}")
                                return message
                            except json.JSONDecodeError as e:
                                self.logger.warning(
                                    f"Failed to parse JSON: {e}, line: {line}"
                                )
                                return None

                except asyncio.TimeoutError:
                    # No data available, return None
                    pass

            # Check if process is still running
            if self.process.returncode is not None:
                self.logger.warning("SSH process has terminated")
                return None

            return None

        except Exception as e:
            self.logger.error(f"Error reading from SSH stream: {e}")
            return None

    async def __aiter__(self):
        """Async iterator for reading messages."""
        return self

    async def __anext__(self):
        """Get next message."""
        while True:
            message = await self.read()
            if message is not None:
                return message

            # Check if process is still running
            if self.process.poll() is not None:
                raise StopAsyncIteration

            # Small delay to prevent busy waiting
            await asyncio.sleep(0.01)


class DirectSSHWriteStream:
    """Write stream for direct SSH MCP communication."""

    def __init__(self, process, logger: logging.Logger):
        self.process = process  # asyncio subprocess
        self.logger = logger
        self._request_id = 1

    async def write(self, message: Dict[str, Any]) -> None:
        """Write a JSON-RPC message to the SSH process."""
        try:
            if self.process.stdin:
                message_json = json.dumps(message) + "\n"
                self.logger.info(f"Writing to SSH: {message_json.strip()}")

                self.process.stdin.write(message_json.encode())
                await self.process.stdin.drain()
                self.logger.info("Message written and drained")
            else:
                raise RuntimeError("SSH process stdin is not available")

        except Exception as e:
            self.logger.error(f"Error writing to SSH stream: {e}")
            raise

    async def send_request(
        self, method: str, params: Optional[Dict[str, Any]] = None
    ) -> int:
        """Send a JSON-RPC request and return the request ID."""
        request_id = self._request_id
        self._request_id += 1

        message = {"jsonrpc": "2.0", "id": request_id, "method": method}

        if params:
            message["params"] = params

        await self.write(message)
        return request_id

    async def send_notification(
        self, method: str, params: Optional[Dict[str, Any]] = None
    ) -> None:
        """Send a JSON-RPC notification (no response expected)."""
        message = {"jsonrpc": "2.0", "method": method}

        if params:
            message["params"] = params

        await self.write(message)


class DirectMCPSession:
    """Direct MCP session that mimics the MCP SDK ClientSession."""

    def __init__(
        self, read_stream: DirectSSHReadStream, write_stream: DirectSSHWriteStream
    ):
        self.read_stream = read_stream
        self.write_stream = write_stream
        self.logger = logging.getLogger(__name__)
        self._pending_requests = {}
        self._initialized = False

    async def __aenter__(self):
        """Enter async context."""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit async context."""
        pass

    async def initialize(self) -> Dict[str, Any]:
        """Initialize the MCP session."""
        if self._initialized:
            return {"status": "already_initialized"}

        request_id = await self.write_stream.send_request(
            "initialize",
            {
                "protocolVersion": "2024-11-05",
                "capabilities": {"roots": {"listChanged": True}, "sampling": {}},
                "clientInfo": {"name": "direct-mcp-client", "version": "1.0.0"},
            },
        )

        # Wait for response
        response = await self._wait_for_response(request_id)

        if response and response.get("result"):
            # Send the required initialized notification
            await self.write_stream.send_notification("notifications/initialized", {})
            self._initialized = True
            self.logger.info("MCP session initialized successfully")
            return response["result"]
        else:
            raise RuntimeError(f"Failed to initialize MCP session: {response}")

    async def list_tools(self) -> list:
        """List available tools."""
        if not self._initialized:
            await self.initialize()

        request_id = await self.write_stream.send_request(
            "tools/list", {"cursor": None}
        )
        response = await self._wait_for_response(request_id)

        if response and response.get("result"):
            return response["result"].get("tools", [])
        else:
            raise RuntimeError(f"Failed to list tools: {response}")

    async def call_tool(
        self, tool_name: str, arguments: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Call a specific tool."""
        if not self._initialized:
            await self.initialize()

        request_id = await self.write_stream.send_request(
            "tools/call", {"name": tool_name, "arguments": arguments}
        )

        response = await self._wait_for_response(request_id)

        if response and response.get("result"):
            return response["result"]
        else:
            raise RuntimeError(f"Failed to call tool {tool_name}: {response}")

    async def _wait_for_response(
        self, request_id: int, timeout: float = 30.0
    ) -> Optional[Dict[str, Any]]:
        """Wait for a response with the given request ID."""
        import time

        start_time = time.time()

        self.logger.info(f"Waiting for response to request {request_id}")

        while time.time() - start_time < timeout:
            message = await self.read_stream.read()

            if message:
                self.logger.info(f"Received message: {message}")
                # Check if this is the response we're waiting for
                if message.get("id") == request_id:
                    self.logger.info(
                        f"Found matching response for request {request_id}"
                    )
                    return message

                # Handle other messages (notifications, etc.)
                self.logger.debug(f"Received other message: {message}")

            # Small delay to prevent busy waiting
            await asyncio.sleep(0.1)  # Increased delay for debugging

        self.logger.error(f"Timeout waiting for response to request {request_id}")
        raise TimeoutError(f"Timeout waiting for response to request {request_id}")

    async def send_ping(self) -> None:
        """Send a ping (health check)."""
        # For direct implementation, we can just check if the process is alive
        if self.read_stream.process.returncode is not None:
            raise RuntimeError("SSH process has terminated")
