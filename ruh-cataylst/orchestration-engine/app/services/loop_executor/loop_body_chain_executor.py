"""
Loop Body Chain Executor

Handles execution of loop body transition chains:
- loop -> a -> b -> c -> d -> loop (collect result from d)
- Coordinates with orchestration engine for actual transition execution
- Manages chain state and result collection
- Supports complex loop body workflows
"""

import asyncio
import time
from typing import Any, Dict, List, Optional, Set
from app.utils.enhanced_logger import get_logger


class LoopBodyChainExecutor:
    """
    Executes and manages loop body transition chains.
    
    Handles the execution flow:
    1. Start loop body chain with iteration data
    2. Monitor chain execution progress
    3. Detect chain completion
    4. Collect results from chain end
    5. Return aggregated result to loop executor
    """

    def __init__(
        self,
        state_manager,
        transition_handler,
        workflow_utils,
        transitions_by_id: Dict[str, Any],
        user_id: str = None,
    ):
        self.state_manager = state_manager
        self.transition_handler = transition_handler
        self.workflow_utils = workflow_utils
        self.transitions_by_id = transitions_by_id
        self.user_id = user_id
        self.logger = get_logger(__name__)

        # Chain execution state
        self.active_chains: Dict[str, Dict[str, Any]] = {}
        self.chain_results: Dict[str, Any] = {}
        self.completion_callbacks: Dict[str, asyncio.Event] = {}

    async def execute_loop_body_chain(
        self,
        loop_transition_id: str,
        iteration_index: int,
        iteration_item: Any,
        iteration_context: Dict[str, Any],
        loop_body_config: Dict[str, Any],
    ) -> Any:
        """
        Execute a loop body chain for a single iteration.

        Args:
            loop_transition_id: ID of the loop transition
            iteration_index: Index of the current iteration
            iteration_item: Data item for this iteration
            iteration_context: Context data for this iteration
            loop_body_config: Loop body configuration

        Returns:
            Result from the completed loop body chain
        """
        chain_id = f"{loop_transition_id}_iteration_{iteration_index}"
        
        self.logger.info(
            f"🔄 Starting loop body chain execution: {chain_id}"
        )

        try:
            # Initialize chain state
            await self._initialize_chain_state(
                chain_id, loop_transition_id, iteration_index, 
                iteration_item, iteration_context, loop_body_config
            )

            # Start chain execution
            await self._start_chain_execution(chain_id)

            # Wait for chain completion
            result = await self._wait_for_chain_completion(chain_id)

            self.logger.info(
                f"✅ Loop body chain completed: {chain_id}, result: {result}"
            )

            return result

        except Exception as e:
            self.logger.error(
                f"❌ Loop body chain execution failed: {chain_id}, error: {str(e)}"
            )
            raise

        finally:
            # Cleanup chain state
            await self._cleanup_chain_state(chain_id)

    async def _initialize_chain_state(
        self,
        chain_id: str,
        loop_transition_id: str,
        iteration_index: int,
        iteration_item: Any,
        iteration_context: Dict[str, Any],
        loop_body_config: Dict[str, Any],
    ) -> None:
        """Initialize state for a loop body chain execution."""
        
        entry_transitions = loop_body_config.get("entry_transitions", [])
        exit_transitions = loop_body_config.get("exit_transitions", [])
        
        if not entry_transitions:
            # Auto-detect entry transitions from loop output connections
            entry_transitions = await self._auto_detect_entry_transitions(loop_transition_id)
        
        if not exit_transitions:
            # Auto-detect exit transitions from chain analysis
            exit_transitions = await self._auto_detect_exit_transitions(entry_transitions)

        self.active_chains[chain_id] = {
            "loop_transition_id": loop_transition_id,
            "iteration_index": iteration_index,
            "iteration_item": iteration_item,
            "iteration_context": iteration_context,
            "entry_transitions": entry_transitions,
            "exit_transitions": exit_transitions,
            "completed_transitions": set(),
            "pending_transitions": set(entry_transitions),
            "chain_state": "initializing",
            "start_time": time.time(),
            "result": None,
        }

        # Create completion event
        self.completion_callbacks[chain_id] = asyncio.Event()

        self.logger.debug(
            f"🔧 Initialized chain state for {chain_id}: "
            f"entry={entry_transitions}, exit={exit_transitions}"
        )

    async def _start_chain_execution(self, chain_id: str) -> None:
        """Start execution of the loop body chain."""
        
        chain_state = self.active_chains[chain_id]
        entry_transitions = chain_state["entry_transitions"]
        iteration_item = chain_state["iteration_item"]
        iteration_context = chain_state["iteration_context"]

        # Inject iteration data into state manager for entry transitions
        await self._inject_iteration_data_for_chain(
            chain_id, iteration_item, iteration_context
        )

        # Mark chain as running
        chain_state["chain_state"] = "running"

        # Start entry transitions through orchestration engine
        for transition_id in entry_transitions:
            self.logger.debug(
                f"🚀 Starting entry transition: {transition_id} for chain {chain_id}"
            )
            
            # The orchestration engine will execute this transition
            # We need to register for completion notification
            await self._register_transition_completion_callback(
                chain_id, transition_id
            )

        self.logger.info(
            f"🔄 Started loop body chain execution: {chain_id} with {len(entry_transitions)} entry transitions"
        )

    async def _wait_for_chain_completion(self, chain_id: str) -> Any:
        """Wait for the loop body chain to complete and return the result."""
        
        completion_event = self.completion_callbacks[chain_id]
        chain_state = self.active_chains[chain_id]
        
        # Wait for completion with timeout
        timeout = 300  # 5 minutes default timeout
        
        try:
            await asyncio.wait_for(completion_event.wait(), timeout=timeout)
            
            # Chain completed, extract result
            result = chain_state.get("result")
            
            if result is None:
                self.logger.warning(
                    f"⚠️ Chain {chain_id} completed but no result found"
                )
                return None
            
            return result
            
        except asyncio.TimeoutError:
            self.logger.error(
                f"⏰ Chain {chain_id} execution timed out after {timeout} seconds"
            )
            raise

    async def _auto_detect_entry_transitions(self, loop_transition_id: str) -> List[str]:
        """Auto-detect entry transitions from loop output connections."""
        
        loop_transition = self.transitions_by_id.get(loop_transition_id)
        if not loop_transition:
            return []

        entry_transitions = []
        output_data_configs = loop_transition.get("node_info", {}).get("output_data", [])

        for config in output_data_configs:
            # Look for iteration output connections (not exit connections)
            handle_mappings = config.get("output_handle_registry", {}).get("handle_mappings", [])
            
            for mapping in handle_mappings:
                handle_type = mapping.get("handle_type", "standard")
                result_path = mapping.get("result_path", "").lower()
                
                # Check if this is a loop body entry connection
                if (
                    handle_type == "loop_body_entry" or
                    "current_item" in result_path or
                    "iteration" in result_path
                ):
                    to_transition_id = config.get("to_transition_id")
                    if to_transition_id and to_transition_id not in entry_transitions:
                        entry_transitions.append(to_transition_id)

        self.logger.debug(
            f"🔍 Auto-detected entry transitions for {loop_transition_id}: {entry_transitions}"
        )
        
        return entry_transitions

    async def _auto_detect_exit_transitions(self, entry_transitions: List[str]) -> List[str]:
        """Auto-detect exit transitions by analyzing the chain structure."""
        
        # For now, implement a simple heuristic:
        # Exit transitions are those that don't have outgoing connections to other loop body transitions
        # This is a simplified approach - in practice, this would need more sophisticated analysis
        
        exit_transitions = []
        
        # TODO: Implement sophisticated chain analysis
        # For now, assume the first entry transition is also the exit transition
        # This handles simple single-node loop bodies
        if entry_transitions:
            exit_transitions = entry_transitions.copy()

        self.logger.debug(
            f"🔍 Auto-detected exit transitions: {exit_transitions}"
        )
        
        return exit_transitions

    async def _inject_iteration_data_for_chain(
        self, chain_id: str, iteration_item: Any, iteration_context: Dict[str, Any]
    ) -> None:
        """Inject iteration data into state manager for chain execution."""
        
        # Create iteration data that chain transitions can access
        iteration_data = {
            "current_item": iteration_item,
            "iteration_context": iteration_context,
            "chain_id": chain_id,
        }

        # Store with a key that chain transitions can access
        iteration_data_key = f"chain_{chain_id}_iteration_data"
        self.state_manager.store_result(iteration_data_key, iteration_data)

        self.logger.debug(
            f"💾 Injected iteration data for chain {chain_id}: {iteration_data}"
        )

    async def _register_transition_completion_callback(
        self, chain_id: str, transition_id: str
    ) -> None:
        """Register to be notified when a transition completes."""
        
        # TODO: Implement transition completion notification system
        # This would integrate with the orchestration engine to get notified
        # when transitions complete
        
        self.logger.debug(
            f"📝 Registered completion callback for transition {transition_id} in chain {chain_id}"
        )

    async def _cleanup_chain_state(self, chain_id: str) -> None:
        """Clean up state for a completed chain."""
        
        # Remove from active chains
        if chain_id in self.active_chains:
            del self.active_chains[chain_id]
        
        # Remove completion callback
        if chain_id in self.completion_callbacks:
            del self.completion_callbacks[chain_id]
        
        # Clean up any temporary state data
        iteration_data_key = f"chain_{chain_id}_iteration_data"
        try:
            # Remove iteration data from state manager
            # TODO: Implement state cleanup in state manager
            pass
        except:
            pass

        self.logger.debug(f"🧹 Cleaned up chain state for {chain_id}")

    def notify_transition_completion(
        self, transition_id: str, result: Any
    ) -> None:
        """
        Notify the chain executor that a transition has completed.
        
        This method should be called by the orchestration engine when
        transitions complete.
        """
        
        # Find which chain this transition belongs to
        for chain_id, chain_state in self.active_chains.items():
            if transition_id in chain_state.get("pending_transitions", set()):
                asyncio.create_task(
                    self._handle_transition_completion(chain_id, transition_id, result)
                )
                break

    async def _handle_transition_completion(
        self, chain_id: str, transition_id: str, result: Any
    ) -> None:
        """Handle completion of a transition in a chain."""
        
        chain_state = self.active_chains.get(chain_id)
        if not chain_state:
            return

        # Mark transition as completed
        chain_state["completed_transitions"].add(transition_id)
        chain_state["pending_transitions"].discard(transition_id)

        self.logger.debug(
            f"✅ Transition {transition_id} completed in chain {chain_id}"
        )

        # Check if this is an exit transition
        if transition_id in chain_state["exit_transitions"]:
            # This is an exit transition - collect the result
            chain_state["result"] = result
            chain_state["chain_state"] = "completed"
            
            # Signal completion
            completion_event = self.completion_callbacks.get(chain_id)
            if completion_event:
                completion_event.set()
            
            self.logger.info(
                f"🏁 Chain {chain_id} completed with exit transition {transition_id}"
            )
