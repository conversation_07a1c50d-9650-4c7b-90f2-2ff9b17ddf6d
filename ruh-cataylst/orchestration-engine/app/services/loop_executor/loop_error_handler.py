"""
Enhanced error handling for loop execution.

This module provides comprehensive error classification, recovery strategies,
and debugging tools for loop execution failures.
"""

import asyncio
import traceback
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
import logging


class ErrorSeverity(Enum):
    """Error severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""

    NETWORK = "network"
    TIMEOUT = "timeout"
    VALIDATION = "validation"
    RESOURCE = "resource"
    LOGIC = "logic"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""

    RETRY = "retry"
    SKIP = "skip"
    FALLBACK = "fallback"
    ABORT = "abort"
    CONTINUE = "continue"


@dataclass
class LoopError:
    """Structured representation of a loop execution error."""

    iteration_index: int
    error: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    recovery_strategy: RecoveryStrategy
    timestamp: float
    context: Dict[str, Any]
    stack_trace: str
    retry_count: int = 0
    resolved: bool = False


class LoopErrorHandler:
    """
    Enhanced error handler for loop execution with classification,
    recovery strategies, and debugging capabilities.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the error handler.

        Args:
            logger: Logger instance for error reporting
        """
        self.logger = logger or logging.getLogger(__name__)
        self.error_history: List[LoopError] = []
        self.error_patterns: Dict[str, Dict[str, Any]] = {}
        self.recovery_handlers: Dict[RecoveryStrategy, Callable] = {}
        self.error_statistics: Dict[str, int] = {}

        # New schema error handling configuration
        self.error_handling_config: Dict[str, Any] = {}

        # Phase 3: Advanced Error Handling Features
        self.failure_threshold_tracker: Dict[str, int] = {}
        self.error_rate_monitor: Dict[str, List[float]] = {}
        self.adaptive_retry_config: Dict[str, Any] = {}
        self.circuit_breaker_states: Dict[str, Dict[str, Any]] = {}
        self.error_correlation_tracker: Dict[str, List[Dict[str, Any]]] = {}
        self.recovery_success_rates: Dict[str, float] = {}

        # Initialize default recovery handlers
        self._setup_default_recovery_handlers()

    def configure_error_handling(self, error_handling_config: Dict[str, Any]) -> None:
        """
        Configure error handling based on new schema configuration.

        Args:
            error_handling_config: Error handling configuration from loop_config
        """
        self.error_handling_config = error_handling_config.copy()
        self.logger.debug(f"Error handling configured: {error_handling_config}")

    def get_iteration_error_action(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> str:
        """
        Get the error action for iteration errors based on new schema configuration.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            Error action string (retry_once, retry_max, skip_iteration, fail_loop)
        """
        # Get configured action for iteration errors
        on_iteration_error = self.error_handling_config.get(
            "on_iteration_error", "retry_once"
        )

        # Check if we should apply different logic based on error type
        loop_error = self.classify_error(error, iteration_index, context)

        # Override based on error severity for critical errors
        if loop_error.severity == ErrorSeverity.CRITICAL:
            return "fail_loop"

        # Apply configured action
        if on_iteration_error == "retry_once":
            if loop_error.retry_count >= 1:
                return "skip_iteration"
            return "retry_once"
        elif on_iteration_error == "retry_max":
            max_retries = self.error_handling_config.get("max_retries", 3)
            if loop_error.retry_count >= max_retries:
                return "skip_iteration"
            return "retry_max"
        elif on_iteration_error == "skip_iteration":
            return "skip_iteration"
        elif on_iteration_error == "fail_loop":
            return "fail_loop"
        else:
            # Default to retry_once for unknown actions
            return "retry_once"

    def should_include_errors_in_results(self) -> bool:
        """
        Check if errors should be included in final results.

        Returns:
            True if errors should be included in results
        """
        return self.error_handling_config.get("include_errors", True)

    def get_error_inclusion_format(self) -> str:
        """
        Get the format for including errors in results.

        Returns:
            Error inclusion format (summary, detailed, minimal)
        """
        return self.error_handling_config.get("error_format", "summary")

    def should_continue_on_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> bool:
        """
        Determine if loop should continue after an error based on new schema configuration.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            True if loop should continue, False if it should stop
        """
        action = self.get_iteration_error_action(error, iteration_index, context)

        # Only fail_loop should stop the loop
        return action != "fail_loop"

    def format_error_for_results(self, loop_error: LoopError) -> Dict[str, Any]:
        """
        Format error for inclusion in results based on configuration.

        Args:
            loop_error: The loop error to format

        Returns:
            Formatted error data
        """
        error_format = self.get_error_inclusion_format()

        if error_format == "minimal":
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "category": loop_error.category.value,
            }
        elif error_format == "detailed":
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "error_type": type(loop_error.error).__name__,
                "category": loop_error.category.value,
                "severity": loop_error.severity.value,
                "recovery_strategy": loop_error.recovery_strategy.value,
                "retry_count": loop_error.retry_count,
                "timestamp": loop_error.timestamp,
                "context": loop_error.context,
                "stack_trace": loop_error.stack_trace,
                "resolved": loop_error.resolved,
            }
        else:  # summary format (default)
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "error_type": type(loop_error.error).__name__,
                "category": loop_error.category.value,
                "severity": loop_error.severity.value,
                "retry_count": loop_error.retry_count,
                "resolved": loop_error.resolved,
            }

    def get_errors_for_results(self) -> List[Dict[str, Any]]:
        """
        Get formatted errors for inclusion in final results.

        Returns:
            List of formatted error data
        """
        if not self.should_include_errors_in_results():
            return []

        return [self.format_error_for_results(error) for error in self.error_history]

    def classify_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> LoopError:
        """
        Classify an error and determine appropriate recovery strategy.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context when error occurred

        Returns:
            Classified LoopError instance
        """
        import time

        # Classify error category
        category = self._classify_error_category(error)

        # Determine severity
        severity = self._determine_error_severity(error, category, context)

        # Select recovery strategy
        recovery_strategy = self._select_recovery_strategy(error, category, severity)

        # Create structured error
        loop_error = LoopError(
            iteration_index=iteration_index,
            error=error,
            category=category,
            severity=severity,
            recovery_strategy=recovery_strategy,
            timestamp=time.time(),
            context=context.copy(),
            stack_trace=traceback.format_exc(),
        )

        # Update statistics
        self._update_error_statistics(loop_error)

        # Store in history
        self.error_history.append(loop_error)

        self.logger.warning(
            f"🚨 Error classified: {category.value}/{severity.value} "
            f"at iteration {iteration_index} - Strategy: {recovery_strategy.value}"
        )

        return loop_error

    def _classify_error_category(self, error: Exception) -> ErrorCategory:
        """Classify error into appropriate category."""
        error_message = str(error).lower()
        error_type = type(error).__name__.lower()

        # Network-related errors
        if any(
            pattern in error_message
            for pattern in [
                "connection",
                "network",
                "socket",
                "dns",
                "host",
                "unreachable",
            ]
        ) or any(
            pattern in error_type for pattern in ["connection", "socket", "network"]
        ):
            return ErrorCategory.NETWORK

        # Timeout errors
        if (
            any(
                pattern in error_message
                for pattern in ["timeout", "timed out", "deadline"]
            )
            or "timeout" in error_type
        ):
            return ErrorCategory.TIMEOUT

        # Validation errors
        if any(
            pattern in error_message
            for pattern in ["validation", "invalid", "malformed", "schema"]
        ) or any(pattern in error_type for pattern in ["validation", "value", "type"]):
            return ErrorCategory.VALIDATION

        # Resource errors
        if any(
            pattern in error_message
            for pattern in ["memory", "disk", "space", "resource", "limit", "quota"]
        ) or any(pattern in error_type for pattern in ["memory", "resource"]):
            return ErrorCategory.RESOURCE

        # System errors
        if any(
            pattern in error_message
            for pattern in ["system", "os", "permission", "access"]
        ) or any(pattern in error_type for pattern in ["os", "system", "permission"]):
            return ErrorCategory.SYSTEM

        # Logic errors
        if any(
            pattern in error_type
            for pattern in ["assertion", "logic", "runtime", "attribute", "key"]
        ):
            return ErrorCategory.LOGIC

        return ErrorCategory.UNKNOWN

    def _determine_error_severity(
        self, error: Exception, category: ErrorCategory, context: Dict[str, Any]
    ) -> ErrorSeverity:
        """Determine error severity based on error type and context."""
        # Critical errors that should stop execution
        if category == ErrorCategory.SYSTEM or isinstance(
            error, (MemoryError, SystemExit, KeyboardInterrupt)
        ):
            return ErrorSeverity.CRITICAL

        # High severity for resource and validation errors
        if category in [ErrorCategory.RESOURCE, ErrorCategory.VALIDATION]:
            return ErrorSeverity.HIGH

        # Medium severity for network and timeout errors (often retryable)
        if category in [ErrorCategory.NETWORK, ErrorCategory.TIMEOUT]:
            return ErrorSeverity.MEDIUM

        # Low severity for logic errors (might be data-specific)
        if category == ErrorCategory.LOGIC:
            return ErrorSeverity.LOW

        return ErrorSeverity.MEDIUM

    def _select_recovery_strategy(
        self, error: Exception, category: ErrorCategory, severity: ErrorSeverity
    ) -> RecoveryStrategy:
        """Select appropriate recovery strategy."""
        # Critical errors should abort
        if severity == ErrorSeverity.CRITICAL:
            return RecoveryStrategy.ABORT

        # Network and timeout errors are often retryable
        if category in [ErrorCategory.NETWORK, ErrorCategory.TIMEOUT]:
            return RecoveryStrategy.RETRY

        # Validation errors should be skipped (bad data)
        if category == ErrorCategory.VALIDATION:
            return RecoveryStrategy.SKIP

        # Resource errors might need fallback
        if category == ErrorCategory.RESOURCE:
            return RecoveryStrategy.FALLBACK

        # Logic errors can often be skipped
        if category == ErrorCategory.LOGIC:
            return RecoveryStrategy.SKIP

        # Default to retry for unknown errors
        return RecoveryStrategy.RETRY

    def _setup_default_recovery_handlers(self) -> None:
        """Setup default recovery handlers for each strategy."""
        self.recovery_handlers = {
            RecoveryStrategy.RETRY: self._handle_retry_recovery,
            RecoveryStrategy.SKIP: self._handle_skip_recovery,
            RecoveryStrategy.FALLBACK: self._handle_fallback_recovery,
            RecoveryStrategy.ABORT: self._handle_abort_recovery,
            RecoveryStrategy.CONTINUE: self._handle_continue_recovery,
        }

    async def _handle_retry_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle retry recovery strategy."""
        max_retries = loop_error.context.get("max_retries", 3)

        if loop_error.retry_count >= max_retries:
            self.logger.error(
                f"❌ Max retries ({max_retries}) exceeded for iteration {loop_error.iteration_index}"
            )
            return {"action": "skip", "reason": "max_retries_exceeded"}

        # Calculate delay with exponential backoff
        delay = min(2**loop_error.retry_count, 60)  # Max 60 seconds

        self.logger.info(
            f"🔄 Retrying iteration {loop_error.iteration_index} "
            f"(attempt {loop_error.retry_count + 1}/{max_retries}) in {delay}s"
        )

        await asyncio.sleep(delay)
        loop_error.retry_count += 1

        return {"action": "retry", "delay": delay}

    async def _handle_skip_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle skip recovery strategy."""
        self.logger.warning(
            f"⏭️ Skipping iteration {loop_error.iteration_index} due to {loop_error.category.value} error"
        )
        loop_error.resolved = True
        return {"action": "skip", "reason": loop_error.category.value}

    async def _handle_fallback_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle fallback recovery strategy."""
        self.logger.info(
            f"🔄 Applying fallback for iteration {loop_error.iteration_index}"
        )

        # Implement fallback logic (simplified version)
        fallback_result = {
            "status": "fallback",
            "iteration_index": loop_error.iteration_index,
            "original_error": str(loop_error.error),
            "fallback_applied": True,
        }

        loop_error.resolved = True
        return {"action": "fallback", "result": fallback_result}

    async def _handle_abort_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle abort recovery strategy."""
        self.logger.error(
            f"🛑 Aborting loop execution due to critical error at iteration {loop_error.iteration_index}"
        )
        return {"action": "abort", "reason": "critical_error"}

    async def _handle_continue_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle continue recovery strategy."""
        self.logger.info(
            f"➡️ Continuing execution despite error at iteration {loop_error.iteration_index}"
        )
        loop_error.resolved = True
        return {"action": "continue"}

    def _update_error_statistics(self, loop_error: LoopError) -> None:
        """Update error statistics for analysis."""
        category_key = f"category_{loop_error.category.value}"
        severity_key = f"severity_{loop_error.severity.value}"
        strategy_key = f"strategy_{loop_error.recovery_strategy.value}"

        self.error_statistics[category_key] = (
            self.error_statistics.get(category_key, 0) + 1
        )
        self.error_statistics[severity_key] = (
            self.error_statistics.get(severity_key, 0) + 1
        )
        self.error_statistics[strategy_key] = (
            self.error_statistics.get(strategy_key, 0) + 1
        )

    async def handle_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Main error handling entry point.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            Recovery action dictionary
        """
        # Classify the error
        loop_error = self.classify_error(error, iteration_index, context)

        # Apply recovery strategy
        recovery_handler = self.recovery_handlers.get(loop_error.recovery_strategy)
        if recovery_handler:
            return await recovery_handler(loop_error)

        # Default fallback
        self.logger.error(
            f"No recovery handler for strategy: {loop_error.recovery_strategy}"
        )
        return {"action": "skip", "reason": "no_recovery_handler"}

    # Phase 3: Advanced Error Handling Features

    def configure_advanced_error_handling(self, config: Dict[str, Any]) -> None:
        """
        Configure advanced error handling features.

        Args:
            config: Advanced error handling configuration
        """
        self.adaptive_retry_config = config.get("adaptive_retry", {
            "enabled": True,
            "base_delay": 1.0,
            "max_delay": 60.0,
            "backoff_multiplier": 2.0,
            "jitter": True,
            "success_rate_threshold": 0.8
        })

        circuit_breaker_config = config.get("circuit_breaker", {})
        self.circuit_breaker_states = {
            "failure_threshold": circuit_breaker_config.get("failure_threshold", 5),
            "recovery_timeout": circuit_breaker_config.get("recovery_timeout", 60.0),
            "success_threshold": circuit_breaker_config.get("success_threshold", 3),
            "state": "closed",  # closed, open, half_open
            "failure_count": 0,
            "last_failure_time": 0.0,
            "success_count": 0
        }

        self.failure_threshold_tracker = {}
        self.error_rate_monitor = {}
        self.error_correlation_tracker = {}
        self.recovery_success_rates = {}

    async def handle_error_with_advanced_features(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Enhanced error handling with advanced features.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            Advanced recovery action dictionary
        """
        # Check circuit breaker state
        if not self._check_circuit_breaker():
            return {
                "action": "circuit_open",
                "reason": "circuit_breaker_open",
                "retry_after": self.circuit_breaker_states["recovery_timeout"]
            }

        # Classify error with enhanced analysis
        loop_error = self.classify_error(error, iteration_index, context)

        # Update error tracking
        self._update_error_tracking(loop_error)

        # Check failure thresholds
        if self._check_failure_threshold(loop_error):
            return {
                "action": "threshold_exceeded",
                "reason": "failure_threshold_exceeded",
                "error_category": loop_error.category.value
            }

        # Apply adaptive retry strategy
        if loop_error.recovery_strategy == RecoveryStrategy.RETRY:
            return await self._handle_adaptive_retry(loop_error)

        # Apply correlation-based recovery
        correlated_strategy = self._get_correlated_recovery_strategy(loop_error)
        if correlated_strategy:
            return await self._apply_correlated_recovery(loop_error, correlated_strategy)

        # Fall back to standard error handling
        return await self.handle_error(error, iteration_index, context)

    def _check_circuit_breaker(self) -> bool:
        """Check if circuit breaker allows execution."""
        import time

        cb_state = self.circuit_breaker_states
        current_time = time.time()

        if cb_state["state"] == "open":
            # Check if recovery timeout has passed
            if current_time - cb_state["last_failure_time"] > cb_state["recovery_timeout"]:
                cb_state["state"] = "half_open"
                cb_state["success_count"] = 0
                self.logger.info("🔄 Circuit breaker transitioning to half-open state")
                return True
            return False

        return True

    def _update_circuit_breaker(self, success: bool) -> None:
        """Update circuit breaker state based on execution result."""
        import time

        cb_state = self.circuit_breaker_states
        current_time = time.time()

        if success:
            cb_state["success_count"] += 1
            if cb_state["state"] == "half_open":
                if cb_state["success_count"] >= cb_state["success_threshold"]:
                    cb_state["state"] = "closed"
                    cb_state["failure_count"] = 0
                    self.logger.info("✅ Circuit breaker closed - service recovered")
        else:
            cb_state["failure_count"] += 1
            cb_state["last_failure_time"] = current_time

            if cb_state["state"] in ["closed", "half_open"]:
                if cb_state["failure_count"] >= cb_state["failure_threshold"]:
                    cb_state["state"] = "open"
                    cb_state["success_count"] = 0
                    self.logger.warning("🚨 Circuit breaker opened - service failing")

    def _update_error_tracking(self, loop_error: LoopError) -> None:
        """Update error tracking metrics."""
        import time

        error_key = f"{loop_error.category.value}_{type(loop_error.error).__name__}"
        current_time = time.time()

        # Update failure threshold tracker
        if error_key not in self.failure_threshold_tracker:
            self.failure_threshold_tracker[error_key] = 0
        self.failure_threshold_tracker[error_key] += 1

        # Update error rate monitor (sliding window of 5 minutes)
        if error_key not in self.error_rate_monitor:
            self.error_rate_monitor[error_key] = []

        self.error_rate_monitor[error_key].append(current_time)

        # Clean old entries (older than 5 minutes)
        cutoff_time = current_time - 300  # 5 minutes
        self.error_rate_monitor[error_key] = [
            t for t in self.error_rate_monitor[error_key] if t > cutoff_time
        ]

        # Update error correlation tracking
        if error_key not in self.error_correlation_tracker:
            self.error_correlation_tracker[error_key] = []

        correlation_entry = {
            "timestamp": current_time,
            "iteration_index": loop_error.iteration_index,
            "context_hash": hash(str(sorted(loop_error.context.items()))),
            "recovery_strategy": loop_error.recovery_strategy.value
        }
        self.error_correlation_tracker[error_key].append(correlation_entry)

        # Keep only recent correlations (last 100 entries)
        if len(self.error_correlation_tracker[error_key]) > 100:
            self.error_correlation_tracker[error_key] = self.error_correlation_tracker[error_key][-100:]

    def _check_failure_threshold(self, loop_error: LoopError) -> bool:
        """Check if failure threshold has been exceeded."""
        error_key = f"{loop_error.category.value}_{type(loop_error.error).__name__}"
        failure_count = self.failure_threshold_tracker.get(error_key, 0)

        # Get threshold from configuration or use defaults
        category_thresholds = {
            "NETWORK": 10,
            "TIMEOUT": 8,
            "VALIDATION": 15,
            "RESOURCE": 5,
            "LOGIC": 20,
            "CRITICAL": 1,
            "UNKNOWN": 12
        }

        threshold = category_thresholds.get(loop_error.category.value, 10)

        if failure_count >= threshold:
            self.logger.warning(
                f"🚨 Failure threshold exceeded for {error_key}: {failure_count}/{threshold}"
            )
            return True

        return False

    async def _handle_adaptive_retry(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle retry with adaptive delay calculation."""
        import random
        import time

        if not self.adaptive_retry_config.get("enabled", True):
            return await self._handle_retry_recovery(loop_error)

        # Calculate adaptive delay
        base_delay = self.adaptive_retry_config.get("base_delay", 1.0)
        max_delay = self.adaptive_retry_config.get("max_delay", 60.0)
        backoff_multiplier = self.adaptive_retry_config.get("backoff_multiplier", 2.0)
        jitter_enabled = self.adaptive_retry_config.get("jitter", True)

        # Calculate delay with exponential backoff
        delay = min(base_delay * (backoff_multiplier ** loop_error.retry_count), max_delay)

        # Add jitter to prevent thundering herd
        if jitter_enabled:
            jitter = random.uniform(0.1, 0.3) * delay
            delay += jitter

        # Adjust delay based on error rate
        error_key = f"{loop_error.category.value}_{type(loop_error.error).__name__}"
        error_rate = self._calculate_error_rate(error_key)

        if error_rate > 0.5:  # High error rate
            delay *= 1.5  # Increase delay
        elif error_rate < 0.1:  # Low error rate
            delay *= 0.7  # Decrease delay

        # Check max retries with adaptive adjustment
        max_retries = loop_error.context.get("max_retries", 3)
        success_rate = self.recovery_success_rates.get(error_key, 1.0)

        if success_rate < self.adaptive_retry_config.get("success_rate_threshold", 0.8):
            max_retries = max(1, max_retries - 1)  # Reduce retries for low success rate

        if loop_error.retry_count >= max_retries:
            self.logger.error(
                f"❌ Adaptive max retries ({max_retries}) exceeded for iteration {loop_error.iteration_index}"
            )
            return {"action": "skip", "reason": "adaptive_max_retries_exceeded"}

        self.logger.info(
            f"🔄 Adaptive retry for iteration {loop_error.iteration_index} "
            f"(attempt {loop_error.retry_count + 1}/{max_retries}) in {delay:.2f}s "
            f"(error_rate: {error_rate:.2f}, success_rate: {success_rate:.2f})"
        )

        await asyncio.sleep(delay)
        loop_error.retry_count += 1

        return {
            "action": "adaptive_retry",
            "delay": delay,
            "error_rate": error_rate,
            "success_rate": success_rate,
            "adjusted_max_retries": max_retries
        }

    def _calculate_error_rate(self, error_key: str) -> float:
        """Calculate error rate for a specific error type."""
        import time

        if error_key not in self.error_rate_monitor:
            return 0.0

        current_time = time.time()
        recent_errors = self.error_rate_monitor[error_key]

        if not recent_errors:
            return 0.0

        # Calculate rate over last 5 minutes
        time_window = 300  # 5 minutes
        error_count = len(recent_errors)

        # Estimate total operations (this is simplified - in practice you'd track this)
        estimated_operations = max(error_count * 10, 100)  # Assume 10:1 success:error ratio minimum

        return min(error_count / estimated_operations, 1.0)

    def _get_correlated_recovery_strategy(self, loop_error: LoopError) -> Optional[str]:
        """Get recovery strategy based on error correlation analysis."""
        error_key = f"{loop_error.category.value}_{type(loop_error.error).__name__}"

        if error_key not in self.error_correlation_tracker:
            return None

        correlations = self.error_correlation_tracker[error_key]

        if len(correlations) < 3:  # Need minimum data for correlation
            return None

        # Analyze recent correlations
        recent_correlations = correlations[-10:]  # Last 10 occurrences

        # Check for patterns in recovery strategies
        strategy_counts = {}
        for correlation in recent_correlations:
            strategy = correlation["recovery_strategy"]
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        # If a strategy has been consistently failing, try a different approach
        most_used_strategy = max(strategy_counts.items(), key=lambda x: x[1])[0]

        if strategy_counts[most_used_strategy] >= 7:  # 70% of recent attempts
            # Try alternative strategy
            alternative_strategies = {
                "RETRY": "SKIP",
                "SKIP": "FALLBACK",
                "FALLBACK": "RETRY",
                "ABORT": "SKIP",
                "CONTINUE": "SKIP"
            }
            return alternative_strategies.get(most_used_strategy)

        return None

    async def _apply_correlated_recovery(
        self, loop_error: LoopError, strategy: str
    ) -> Dict[str, Any]:
        """Apply correlated recovery strategy."""
        self.logger.info(
            f"🔍 Applying correlated recovery strategy '{strategy}' for iteration {loop_error.iteration_index}"
        )

        # Map string strategy to RecoveryStrategy enum
        strategy_mapping = {
            "RETRY": RecoveryStrategy.RETRY,
            "SKIP": RecoveryStrategy.SKIP,
            "FALLBACK": RecoveryStrategy.FALLBACK,
            "ABORT": RecoveryStrategy.ABORT,
            "CONTINUE": RecoveryStrategy.CONTINUE
        }

        recovery_strategy = strategy_mapping.get(strategy, RecoveryStrategy.SKIP)

        # Apply the strategy
        recovery_handler = self.recovery_handlers.get(recovery_strategy)
        if recovery_handler:
            result = await recovery_handler(loop_error)
            result["correlated_strategy"] = True
            result["original_strategy"] = loop_error.recovery_strategy.value
            result["applied_strategy"] = strategy
            return result

        # Fallback
        return {"action": "skip", "reason": "correlated_strategy_failed"}

    def update_recovery_success_rate(self, error_key: str, success: bool) -> None:
        """Update recovery success rate for error tracking."""
        if error_key not in self.recovery_success_rates:
            self.recovery_success_rates[error_key] = 1.0

        # Use exponential moving average
        alpha = 0.1  # Learning rate
        current_rate = self.recovery_success_rates[error_key]
        new_value = 1.0 if success else 0.0

        self.recovery_success_rates[error_key] = (
            alpha * new_value + (1 - alpha) * current_rate
        )

    def get_error_analytics(self) -> Dict[str, Any]:
        """Get comprehensive error analytics."""
        import time

        current_time = time.time()

        analytics = {
            "failure_thresholds": dict(self.failure_threshold_tracker),
            "error_rates": {},
            "recovery_success_rates": dict(self.recovery_success_rates),
            "circuit_breaker_state": dict(self.circuit_breaker_states),
            "total_errors": len(self.error_history),
            "error_categories": {},
            "recent_error_trends": {}
        }

        # Calculate error rates
        for error_key, timestamps in self.error_rate_monitor.items():
            analytics["error_rates"][error_key] = self._calculate_error_rate(error_key)

        # Analyze error categories
        for error in self.error_history:
            category = error.category.value
            analytics["error_categories"][category] = analytics["error_categories"].get(category, 0) + 1

        # Recent error trends (last hour)
        hour_ago = current_time - 3600
        recent_errors = [e for e in self.error_history if e.timestamp > hour_ago]

        for error in recent_errors:
            category = error.category.value
            analytics["recent_error_trends"][category] = analytics["recent_error_trends"].get(category, 0) + 1

        return analytics

    def get_error_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive error report.

        Returns:
            Error analysis report
        """
        if not self.error_history:
            return {"total_errors": 0, "message": "No errors recorded"}

        # Calculate statistics
        total_errors = len(self.error_history)
        resolved_errors = sum(1 for e in self.error_history if e.resolved)

        # Group by category and severity
        category_counts = {}
        severity_counts = {}

        for error in self.error_history:
            category_counts[error.category.value] = (
                category_counts.get(error.category.value, 0) + 1
            )
            severity_counts[error.severity.value] = (
                severity_counts.get(error.severity.value, 0) + 1
            )

        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": (
                resolved_errors / total_errors if total_errors > 0 else 0
            ),
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "error_statistics": self.error_statistics,
            "recent_errors": [
                {
                    "iteration": e.iteration_index,
                    "category": e.category.value,
                    "severity": e.severity.value,
                    "strategy": e.recovery_strategy.value,
                    "resolved": e.resolved,
                    "retry_count": e.retry_count,
                }
                for e in self.error_history[-10:]  # Last 10 errors
            ],
        }
