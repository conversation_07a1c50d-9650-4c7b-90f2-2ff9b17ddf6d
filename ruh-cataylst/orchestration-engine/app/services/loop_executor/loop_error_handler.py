"""
Enhanced error handling for loop execution.

This module provides comprehensive error classification, recovery strategies,
and debugging tools for loop execution failures.
"""

import asyncio
import traceback
from enum import Enum
from typing import Any, Dict, List, Optional, Callable
from dataclasses import dataclass
import logging


class ErrorSeverity(Enum):
    """Error severity levels."""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""

    NETWORK = "network"
    TIMEOUT = "timeout"
    VALIDATION = "validation"
    RESOURCE = "resource"
    LOGIC = "logic"
    SYSTEM = "system"
    UNKNOWN = "unknown"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""

    RETRY = "retry"
    SKIP = "skip"
    FALLBACK = "fallback"
    ABORT = "abort"
    CONTINUE = "continue"


@dataclass
class LoopError:
    """Structured representation of a loop execution error."""

    iteration_index: int
    error: Exception
    category: ErrorCategory
    severity: ErrorSeverity
    recovery_strategy: RecoveryStrategy
    timestamp: float
    context: Dict[str, Any]
    stack_trace: str
    retry_count: int = 0
    resolved: bool = False


class LoopErrorHandler:
    """
    Enhanced error handler for loop execution with classification,
    recovery strategies, and debugging capabilities.
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        Initialize the error handler.

        Args:
            logger: Logger instance for error reporting
        """
        self.logger = logger or logging.getLogger(__name__)
        self.error_history: List[LoopError] = []
        self.error_patterns: Dict[str, Dict[str, Any]] = {}
        self.recovery_handlers: Dict[RecoveryStrategy, Callable] = {}
        self.error_statistics: Dict[str, int] = {}

        # New schema error handling configuration
        self.error_handling_config: Dict[str, Any] = {}

        # Initialize default recovery handlers
        self._setup_default_recovery_handlers()

    def configure_error_handling(self, error_handling_config: Dict[str, Any]) -> None:
        """
        Configure error handling based on new schema configuration.

        Args:
            error_handling_config: Error handling configuration from loop_config
        """
        self.error_handling_config = error_handling_config.copy()
        self.logger.debug(f"Error handling configured: {error_handling_config}")

    def get_iteration_error_action(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> str:
        """
        Get the error action for iteration errors based on new schema configuration.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            Error action string (retry_once, retry_max, skip_iteration, fail_loop)
        """
        # Get configured action for iteration errors
        on_iteration_error = self.error_handling_config.get(
            "on_iteration_error", "retry_once"
        )

        # Check if we should apply different logic based on error type
        loop_error = self.classify_error(error, iteration_index, context)

        # Override based on error severity for critical errors
        if loop_error.severity == ErrorSeverity.CRITICAL:
            return "fail_loop"

        # Apply configured action
        if on_iteration_error == "retry_once":
            if loop_error.retry_count >= 1:
                return "skip_iteration"
            return "retry_once"
        elif on_iteration_error == "retry_max":
            max_retries = self.error_handling_config.get("max_retries", 3)
            if loop_error.retry_count >= max_retries:
                return "skip_iteration"
            return "retry_max"
        elif on_iteration_error == "skip_iteration":
            return "skip_iteration"
        elif on_iteration_error == "fail_loop":
            return "fail_loop"
        else:
            # Default to retry_once for unknown actions
            return "retry_once"

    def should_include_errors_in_results(self) -> bool:
        """
        Check if errors should be included in final results.

        Returns:
            True if errors should be included in results
        """
        return self.error_handling_config.get("include_errors", True)

    def get_error_inclusion_format(self) -> str:
        """
        Get the format for including errors in results.

        Returns:
            Error inclusion format (summary, detailed, minimal)
        """
        return self.error_handling_config.get("error_format", "summary")

    def should_continue_on_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> bool:
        """
        Determine if loop should continue after an error based on new schema configuration.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            True if loop should continue, False if it should stop
        """
        action = self.get_iteration_error_action(error, iteration_index, context)

        # Only fail_loop should stop the loop
        return action != "fail_loop"

    def format_error_for_results(self, loop_error: LoopError) -> Dict[str, Any]:
        """
        Format error for inclusion in results based on configuration.

        Args:
            loop_error: The loop error to format

        Returns:
            Formatted error data
        """
        error_format = self.get_error_inclusion_format()

        if error_format == "minimal":
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "category": loop_error.category.value,
            }
        elif error_format == "detailed":
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "error_type": type(loop_error.error).__name__,
                "category": loop_error.category.value,
                "severity": loop_error.severity.value,
                "recovery_strategy": loop_error.recovery_strategy.value,
                "retry_count": loop_error.retry_count,
                "timestamp": loop_error.timestamp,
                "context": loop_error.context,
                "stack_trace": loop_error.stack_trace,
                "resolved": loop_error.resolved,
            }
        else:  # summary format (default)
            return {
                "iteration_index": loop_error.iteration_index,
                "error": str(loop_error.error),
                "error_type": type(loop_error.error).__name__,
                "category": loop_error.category.value,
                "severity": loop_error.severity.value,
                "retry_count": loop_error.retry_count,
                "resolved": loop_error.resolved,
            }

    def get_errors_for_results(self) -> List[Dict[str, Any]]:
        """
        Get formatted errors for inclusion in final results.

        Returns:
            List of formatted error data
        """
        if not self.should_include_errors_in_results():
            return []

        return [self.format_error_for_results(error) for error in self.error_history]

    def classify_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> LoopError:
        """
        Classify an error and determine appropriate recovery strategy.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context when error occurred

        Returns:
            Classified LoopError instance
        """
        import time

        # Classify error category
        category = self._classify_error_category(error)

        # Determine severity
        severity = self._determine_error_severity(error, category, context)

        # Select recovery strategy
        recovery_strategy = self._select_recovery_strategy(error, category, severity)

        # Create structured error
        loop_error = LoopError(
            iteration_index=iteration_index,
            error=error,
            category=category,
            severity=severity,
            recovery_strategy=recovery_strategy,
            timestamp=time.time(),
            context=context.copy(),
            stack_trace=traceback.format_exc(),
        )

        # Update statistics
        self._update_error_statistics(loop_error)

        # Store in history
        self.error_history.append(loop_error)

        self.logger.warning(
            f"🚨 Error classified: {category.value}/{severity.value} "
            f"at iteration {iteration_index} - Strategy: {recovery_strategy.value}"
        )

        return loop_error

    def _classify_error_category(self, error: Exception) -> ErrorCategory:
        """Classify error into appropriate category."""
        error_message = str(error).lower()
        error_type = type(error).__name__.lower()

        # Network-related errors
        if any(
            pattern in error_message
            for pattern in [
                "connection",
                "network",
                "socket",
                "dns",
                "host",
                "unreachable",
            ]
        ) or any(
            pattern in error_type for pattern in ["connection", "socket", "network"]
        ):
            return ErrorCategory.NETWORK

        # Timeout errors
        if (
            any(
                pattern in error_message
                for pattern in ["timeout", "timed out", "deadline"]
            )
            or "timeout" in error_type
        ):
            return ErrorCategory.TIMEOUT

        # Validation errors
        if any(
            pattern in error_message
            for pattern in ["validation", "invalid", "malformed", "schema"]
        ) or any(pattern in error_type for pattern in ["validation", "value", "type"]):
            return ErrorCategory.VALIDATION

        # Resource errors
        if any(
            pattern in error_message
            for pattern in ["memory", "disk", "space", "resource", "limit", "quota"]
        ) or any(pattern in error_type for pattern in ["memory", "resource"]):
            return ErrorCategory.RESOURCE

        # System errors
        if any(
            pattern in error_message
            for pattern in ["system", "os", "permission", "access"]
        ) or any(pattern in error_type for pattern in ["os", "system", "permission"]):
            return ErrorCategory.SYSTEM

        # Logic errors
        if any(
            pattern in error_type
            for pattern in ["assertion", "logic", "runtime", "attribute", "key"]
        ):
            return ErrorCategory.LOGIC

        return ErrorCategory.UNKNOWN

    def _determine_error_severity(
        self, error: Exception, category: ErrorCategory, context: Dict[str, Any]
    ) -> ErrorSeverity:
        """Determine error severity based on error type and context."""
        # Critical errors that should stop execution
        if category == ErrorCategory.SYSTEM or isinstance(
            error, (MemoryError, SystemExit, KeyboardInterrupt)
        ):
            return ErrorSeverity.CRITICAL

        # High severity for resource and validation errors
        if category in [ErrorCategory.RESOURCE, ErrorCategory.VALIDATION]:
            return ErrorSeverity.HIGH

        # Medium severity for network and timeout errors (often retryable)
        if category in [ErrorCategory.NETWORK, ErrorCategory.TIMEOUT]:
            return ErrorSeverity.MEDIUM

        # Low severity for logic errors (might be data-specific)
        if category == ErrorCategory.LOGIC:
            return ErrorSeverity.LOW

        return ErrorSeverity.MEDIUM

    def _select_recovery_strategy(
        self, error: Exception, category: ErrorCategory, severity: ErrorSeverity
    ) -> RecoveryStrategy:
        """Select appropriate recovery strategy."""
        # Critical errors should abort
        if severity == ErrorSeverity.CRITICAL:
            return RecoveryStrategy.ABORT

        # Network and timeout errors are often retryable
        if category in [ErrorCategory.NETWORK, ErrorCategory.TIMEOUT]:
            return RecoveryStrategy.RETRY

        # Validation errors should be skipped (bad data)
        if category == ErrorCategory.VALIDATION:
            return RecoveryStrategy.SKIP

        # Resource errors might need fallback
        if category == ErrorCategory.RESOURCE:
            return RecoveryStrategy.FALLBACK

        # Logic errors can often be skipped
        if category == ErrorCategory.LOGIC:
            return RecoveryStrategy.SKIP

        # Default to retry for unknown errors
        return RecoveryStrategy.RETRY

    def _setup_default_recovery_handlers(self) -> None:
        """Setup default recovery handlers for each strategy."""
        self.recovery_handlers = {
            RecoveryStrategy.RETRY: self._handle_retry_recovery,
            RecoveryStrategy.SKIP: self._handle_skip_recovery,
            RecoveryStrategy.FALLBACK: self._handle_fallback_recovery,
            RecoveryStrategy.ABORT: self._handle_abort_recovery,
            RecoveryStrategy.CONTINUE: self._handle_continue_recovery,
        }

    async def _handle_retry_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle retry recovery strategy."""
        max_retries = loop_error.context.get("max_retries", 3)

        if loop_error.retry_count >= max_retries:
            self.logger.error(
                f"❌ Max retries ({max_retries}) exceeded for iteration {loop_error.iteration_index}"
            )
            return {"action": "skip", "reason": "max_retries_exceeded"}

        # Calculate delay with exponential backoff
        delay = min(2**loop_error.retry_count, 60)  # Max 60 seconds

        self.logger.info(
            f"🔄 Retrying iteration {loop_error.iteration_index} "
            f"(attempt {loop_error.retry_count + 1}/{max_retries}) in {delay}s"
        )

        await asyncio.sleep(delay)
        loop_error.retry_count += 1

        return {"action": "retry", "delay": delay}

    async def _handle_skip_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle skip recovery strategy."""
        self.logger.warning(
            f"⏭️ Skipping iteration {loop_error.iteration_index} due to {loop_error.category.value} error"
        )
        loop_error.resolved = True
        return {"action": "skip", "reason": loop_error.category.value}

    async def _handle_fallback_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle fallback recovery strategy."""
        self.logger.info(
            f"🔄 Applying fallback for iteration {loop_error.iteration_index}"
        )

        # Implement fallback logic (simplified version)
        fallback_result = {
            "status": "fallback",
            "iteration_index": loop_error.iteration_index,
            "original_error": str(loop_error.error),
            "fallback_applied": True,
        }

        loop_error.resolved = True
        return {"action": "fallback", "result": fallback_result}

    async def _handle_abort_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle abort recovery strategy."""
        self.logger.error(
            f"🛑 Aborting loop execution due to critical error at iteration {loop_error.iteration_index}"
        )
        return {"action": "abort", "reason": "critical_error"}

    async def _handle_continue_recovery(self, loop_error: LoopError) -> Dict[str, Any]:
        """Handle continue recovery strategy."""
        self.logger.info(
            f"➡️ Continuing execution despite error at iteration {loop_error.iteration_index}"
        )
        loop_error.resolved = True
        return {"action": "continue"}

    def _update_error_statistics(self, loop_error: LoopError) -> None:
        """Update error statistics for analysis."""
        category_key = f"category_{loop_error.category.value}"
        severity_key = f"severity_{loop_error.severity.value}"
        strategy_key = f"strategy_{loop_error.recovery_strategy.value}"

        self.error_statistics[category_key] = (
            self.error_statistics.get(category_key, 0) + 1
        )
        self.error_statistics[severity_key] = (
            self.error_statistics.get(severity_key, 0) + 1
        )
        self.error_statistics[strategy_key] = (
            self.error_statistics.get(strategy_key, 0) + 1
        )

    async def handle_error(
        self, error: Exception, iteration_index: int, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Main error handling entry point.

        Args:
            error: The exception that occurred
            iteration_index: Index of the failed iteration
            context: Execution context

        Returns:
            Recovery action dictionary
        """
        # Classify the error
        loop_error = self.classify_error(error, iteration_index, context)

        # Apply recovery strategy
        recovery_handler = self.recovery_handlers.get(loop_error.recovery_strategy)
        if recovery_handler:
            return await recovery_handler(loop_error)

        # Default fallback
        self.logger.error(
            f"No recovery handler for strategy: {loop_error.recovery_strategy}"
        )
        return {"action": "skip", "reason": "no_recovery_handler"}

    def get_error_report(self) -> Dict[str, Any]:
        """
        Generate comprehensive error report.

        Returns:
            Error analysis report
        """
        if not self.error_history:
            return {"total_errors": 0, "message": "No errors recorded"}

        # Calculate statistics
        total_errors = len(self.error_history)
        resolved_errors = sum(1 for e in self.error_history if e.resolved)

        # Group by category and severity
        category_counts = {}
        severity_counts = {}

        for error in self.error_history:
            category_counts[error.category.value] = (
                category_counts.get(error.category.value, 0) + 1
            )
            severity_counts[error.severity.value] = (
                severity_counts.get(error.severity.value, 0) + 1
            )

        return {
            "total_errors": total_errors,
            "resolved_errors": resolved_errors,
            "resolution_rate": (
                resolved_errors / total_errors if total_errors > 0 else 0
            ),
            "category_breakdown": category_counts,
            "severity_breakdown": severity_counts,
            "error_statistics": self.error_statistics,
            "recent_errors": [
                {
                    "iteration": e.iteration_index,
                    "category": e.category.value,
                    "severity": e.severity.value,
                    "strategy": e.recovery_strategy.value,
                    "resolved": e.resolved,
                    "retry_count": e.retry_count,
                }
                for e in self.error_history[-10:]  # Last 10 errors
            ],
        }
