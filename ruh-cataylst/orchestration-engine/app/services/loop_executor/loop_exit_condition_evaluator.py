"""
Loop Exit Condition Evaluator.

This module provides evaluation logic for different types of loop exit conditions
in the new loop node architecture.
"""

import time
from typing import Dict, List, Any, Optional
from app.utils.enhanced_logger import get_logger


class LoopExitConditionEvaluator:
    """
    Evaluator for loop exit conditions.
    
    Supports various exit condition types:
    - all_items_processed: Process all items in the iteration source
    - max_iterations: Stop after a maximum number of iterations
    - timeout: Stop after a time limit
    - success_condition: Stop after achieving a success threshold
    - failure_threshold: Stop after too many consecutive failures
    """

    def __init__(self):
        """Initialize the exit condition evaluator."""
        self.logger = get_logger("LoopExitConditionEvaluator")
        self.start_time = None
        self.consecutive_failures = 0
        self.success_count = 0

    def initialize_evaluation(self, exit_condition_config: Dict[str, Any]) -> None:
        """
        Initialize evaluation state for a new loop execution.
        
        Args:
            exit_condition_config: Exit condition configuration from loop_config
        """
        self.exit_condition_config = exit_condition_config
        self.start_time = time.time()
        self.consecutive_failures = 0
        self.success_count = 0
        
        self.logger.debug(f"Exit condition evaluation initialized: {exit_condition_config}")

    def should_exit_loop(
        self, 
        current_iteration: int, 
        total_iterations: int,
        iteration_results: Dict[int, Any],
        loop_metadata: Dict[str, Any]
    ) -> bool:
        """
        Evaluate whether the loop should exit based on the configured condition.
        
        Args:
            current_iteration: Current iteration index
            total_iterations: Total number of iterations planned
            iteration_results: Results from completed iterations
            loop_metadata: Additional loop metadata
            
        Returns:
            True if loop should exit, False otherwise
        """
        if not hasattr(self, 'exit_condition_config'):
            self.logger.warning("Exit condition not initialized, defaulting to all_items_processed")
            return current_iteration >= total_iterations - 1

        condition_type = self.exit_condition_config.get("condition_type", "all_items_processed")
        
        try:
            if condition_type == "all_items_processed":
                return self._evaluate_all_items_processed(current_iteration, total_iterations)
            
            elif condition_type == "max_iterations":
                return self._evaluate_max_iterations(current_iteration)
            
            elif condition_type == "timeout":
                return self._evaluate_timeout()
            
            elif condition_type == "success_condition":
                return self._evaluate_success_condition(iteration_results)
            
            elif condition_type == "failure_threshold":
                return self._evaluate_failure_threshold(iteration_results, current_iteration)
            
            else:
                self.logger.warning(f"Unknown exit condition type: {condition_type}, defaulting to all_items_processed")
                return self._evaluate_all_items_processed(current_iteration, total_iterations)
                
        except Exception as e:
            self.logger.error(f"Error evaluating exit condition: {str(e)}")
            # Default to continuing the loop on error
            return False

    def _evaluate_all_items_processed(self, current_iteration: int, total_iterations: int) -> bool:
        """
        Evaluate all_items_processed condition.
        
        Args:
            current_iteration: Current iteration index
            total_iterations: Total number of iterations
            
        Returns:
            True if all items have been processed
        """
        should_exit = current_iteration >= total_iterations - 1
        if should_exit:
            self.logger.debug(f"All items processed: {current_iteration + 1}/{total_iterations}")
        return should_exit

    def _evaluate_max_iterations(self, current_iteration: int) -> bool:
        """
        Evaluate max_iterations condition.
        
        Args:
            current_iteration: Current iteration index
            
        Returns:
            True if maximum iterations reached
        """
        max_iterations = self.exit_condition_config.get("max_iterations", 100)
        should_exit = current_iteration >= max_iterations - 1
        if should_exit:
            self.logger.debug(f"Maximum iterations reached: {current_iteration + 1}/{max_iterations}")
        return should_exit

    def _evaluate_timeout(self) -> bool:
        """
        Evaluate timeout condition.
        
        Returns:
            True if timeout has been reached
        """
        if not self.start_time:
            return False
            
        timeout_minutes = self.exit_condition_config.get("timeout_minutes", 60)
        elapsed_minutes = (time.time() - self.start_time) / 60
        should_exit = elapsed_minutes >= timeout_minutes
        
        if should_exit:
            self.logger.debug(f"Timeout reached: {elapsed_minutes:.2f}/{timeout_minutes} minutes")
        return should_exit

    def _evaluate_success_condition(self, iteration_results: Dict[int, Any]) -> bool:
        """
        Evaluate success_condition.
        
        Args:
            iteration_results: Results from completed iterations
            
        Returns:
            True if success threshold has been met
        """
        success_threshold = self.exit_condition_config.get("success_threshold", 1)
        
        # Count successful iterations
        success_count = 0
        for result in iteration_results.values():
            if self._is_iteration_successful(result):
                success_count += 1
        
        should_exit = success_count >= success_threshold
        if should_exit:
            self.logger.debug(f"Success threshold reached: {success_count}/{success_threshold}")
        return should_exit

    def _evaluate_failure_threshold(self, iteration_results: Dict[int, Any], current_iteration: int) -> bool:
        """
        Evaluate failure_threshold condition.
        
        Args:
            iteration_results: Results from completed iterations
            current_iteration: Current iteration index
            
        Returns:
            True if failure threshold has been exceeded
        """
        failure_threshold = self.exit_condition_config.get("failure_threshold", 10)
        
        # Count consecutive failures from the end
        consecutive_failures = 0
        for i in range(current_iteration, -1, -1):
            result = iteration_results.get(i)
            if result and self._is_iteration_failed(result):
                consecutive_failures += 1
            else:
                break
        
        should_exit = consecutive_failures >= failure_threshold
        if should_exit:
            self.logger.debug(f"Failure threshold exceeded: {consecutive_failures}/{failure_threshold} consecutive failures")
        return should_exit

    def _is_iteration_successful(self, result: Any) -> bool:
        """
        Determine if an iteration result represents success.
        
        Args:
            result: Iteration result data
            
        Returns:
            True if the iteration was successful
        """
        if isinstance(result, dict):
            # Check for explicit status field
            status = result.get("status")
            if status:
                return status in ["success", "completed", "ok"]
            
            # Check for error field (absence indicates success)
            if "error" not in result:
                return True
            
            # Check for result data (presence indicates success)
            if "result" in result or "data" in result:
                return True
        
        # For non-dict results, assume success if not None
        return result is not None

    def _is_iteration_failed(self, result: Any) -> bool:
        """
        Determine if an iteration result represents failure.
        
        Args:
            result: Iteration result data
            
        Returns:
            True if the iteration failed
        """
        if isinstance(result, dict):
            # Check for explicit status field
            status = result.get("status")
            if status:
                return status in ["failed", "error", "timeout"]
            
            # Check for error field (presence indicates failure)
            if "error" in result:
                return True
        
        # For non-dict results, assume failure if None
        return result is None

    def get_evaluation_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the current evaluation state.
        
        Returns:
            Dictionary containing evaluation summary
        """
        summary = {
            "condition_type": self.exit_condition_config.get("condition_type", "unknown"),
            "start_time": self.start_time,
            "elapsed_time": time.time() - self.start_time if self.start_time else 0,
            "consecutive_failures": self.consecutive_failures,
            "success_count": self.success_count,
        }
        
        # Add condition-specific details
        condition_type = self.exit_condition_config.get("condition_type")
        if condition_type == "max_iterations":
            summary["max_iterations"] = self.exit_condition_config.get("max_iterations")
        elif condition_type == "timeout":
            summary["timeout_minutes"] = self.exit_condition_config.get("timeout_minutes")
        elif condition_type == "success_condition":
            summary["success_threshold"] = self.exit_condition_config.get("success_threshold")
        elif condition_type == "failure_threshold":
            summary["failure_threshold"] = self.exit_condition_config.get("failure_threshold")
        
        return summary

    def reset_evaluation_state(self) -> None:
        """Reset evaluation state for a new loop execution."""
        self.start_time = None
        self.consecutive_failures = 0
        self.success_count = 0
        self.logger.debug("Exit condition evaluation state reset")
