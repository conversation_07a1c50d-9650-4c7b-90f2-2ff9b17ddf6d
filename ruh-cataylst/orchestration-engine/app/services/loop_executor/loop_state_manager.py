import json
from typing import Dict, List, Any, Optional
from app.utils.enhanced_logger import get_logger


class LoopStateManager:
    """
    Loop State Manager for tracking loop-specific execution state.

    This manager handles iteration state, context preservation, and result tracking
    for loop executions within the orchestration engine.
    """

    def __init__(
        self, loop_id: str, transition_id: str, workflow_id: Optional[str] = None
    ):
        """
        Initialize the LoopStateManager.

        Args:
            loop_id: Unique identifier for this loop instance
            transition_id: ID of the transition containing this loop
            workflow_id: Optional workflow ID for context
        """
        self.loop_id = loop_id
        self.transition_id = transition_id
        self.workflow_id = workflow_id
        self.logger = get_logger("LoopStateManager")

        # Loop execution state
        self._current_iteration_index = 0
        self._total_iterations = 0
        self._iteration_status = {}  # {index: status}
        self._loop_execution_state = (
            "initialized"  # initialized, running, completed, error
        )

        # Loop data storage
        self._iteration_results = {}  # {index: result}
        self._iteration_contexts = {}  # {index: context}
        self._loop_metadata = {}

        # New architecture: Output state tracking
        self._iteration_outputs = (
            []
        )  # Track all iteration outputs sent to connected nodes
        self._exit_output = None  # Track final exit output
        self._output_routing_state = "pending"  # pending, iteration_active, exit_sent

        # Exit condition tracking
        self._exit_condition_config = {}
        self._exit_condition_state = "not_met"  # not_met, evaluating, met
        self._exit_condition_results = []

        self.logger.debug(f"LoopStateManager initialized for loop_id: {loop_id}")

    @property
    def current_iteration_index(self) -> int:
        """Get the current iteration index."""
        return self._current_iteration_index

    @current_iteration_index.setter
    def current_iteration_index(self, value: int) -> None:
        """Set the current iteration index."""
        self._current_iteration_index = value
        self.logger.debug(f"Current iteration index set to: {value}")

    @property
    def total_iterations(self) -> int:
        """Get the total number of iterations."""
        return self._total_iterations

    @total_iterations.setter
    def total_iterations(self, value: int) -> None:
        """Set the total number of iterations."""
        self._total_iterations = value
        self.logger.debug(f"Total iterations set to: {value}")

    @property
    def loop_execution_state(self) -> str:
        """Get the current loop execution state."""
        return self._loop_execution_state

    def set_loop_execution_state(self, state: str) -> None:
        """
        Set the loop execution state.

        Args:
            state: New execution state (initialized, running, completed, error)
        """
        valid_states = ["initialized", "running", "completed", "error"]
        if state not in valid_states:
            raise ValueError(
                f"Invalid loop execution state: {state}. Must be one of: {valid_states}"
            )

        self._loop_execution_state = state
        self.logger.debug(f"Loop execution state changed to: {state}")

    def get_iteration_status(self, iteration_index: int) -> Optional[str]:
        """
        Get the status of a specific iteration.

        Args:
            iteration_index: Index of the iteration

        Returns:
            Status string or None if not found
        """
        return self._iteration_status.get(iteration_index)

    def set_iteration_status(self, iteration_index: int, status: str) -> None:
        """
        Set the status of a specific iteration.

        Args:
            iteration_index: Index of the iteration
            status: Status string (pending, running, completed, failed)
        """
        valid_statuses = ["pending", "running", "completed", "failed"]
        if status not in valid_statuses:
            raise ValueError(
                f"Invalid iteration status: {status}. Must be one of: {valid_statuses}"
            )

        self._iteration_status[iteration_index] = status
        self.logger.debug(f"Iteration {iteration_index} status set to: {status}")

    def store_iteration_context(
        self, iteration_index: int, context: Dict[str, Any]
    ) -> None:
        """
        Store context for a specific iteration.

        Args:
            iteration_index: Index of the iteration
            context: Context dictionary to store
        """
        self._iteration_contexts[iteration_index] = context.copy()
        self.logger.debug(f"Context stored for iteration {iteration_index}")

    def retrieve_iteration_context(
        self, iteration_index: int
    ) -> Optional[Dict[str, Any]]:
        """
        Retrieve context for a specific iteration.

        Args:
            iteration_index: Index of the iteration

        Returns:
            Context dictionary or None if not found
        """
        context = self._iteration_contexts.get(iteration_index)
        if context:
            self.logger.debug(f"Context retrieved for iteration {iteration_index}")
        return context

    def get_previous_contexts(self, current_index: int) -> List[Dict[str, Any]]:
        """
        Get contexts from all previous iterations (for context-preserving loops).

        Args:
            current_index: Current iteration index

        Returns:
            List of context dictionaries from previous iterations
        """
        previous_contexts = []
        for i in range(current_index):
            context = self._iteration_contexts.get(i)
            if context:
                previous_contexts.append(context)

        self.logger.debug(
            f"Retrieved {len(previous_contexts)} previous contexts for iteration {current_index}"
        )
        return previous_contexts

    def store_iteration_result(self, iteration_index: int, result: Any) -> None:
        """
        Store result for a specific iteration.

        Args:
            iteration_index: Index of the iteration
            result: Result data to store
        """
        self._iteration_results[iteration_index] = result
        self.logger.debug(f"Result stored for iteration {iteration_index}")

    def retrieve_iteration_result(self, iteration_index: int) -> Any:
        """
        Retrieve result for a specific iteration.

        Args:
            iteration_index: Index of the iteration

        Returns:
            Result data or None if not found
        """
        result = self._iteration_results.get(iteration_index)
        if result is not None:
            self.logger.debug(f"Result retrieved for iteration {iteration_index}")
        return result

    def get_all_results(self) -> Dict[int, Any]:
        """
        Get all iteration results.

        Returns:
            Dictionary mapping iteration indices to results
        """
        return self._iteration_results.copy()

    def get_ordered_results(self) -> List[Any]:
        """
        Get all iteration results in order.

        Returns:
            List of results ordered by iteration index
        """
        ordered_results = []
        for i in sorted(self._iteration_results.keys()):
            ordered_results.append(self._iteration_results[i])

        self.logger.debug(f"Retrieved {len(ordered_results)} ordered results")
        return ordered_results

    def set_loop_metadata(self, key: str, value: Any) -> None:
        """
        Set loop metadata.

        Args:
            key: Metadata key
            value: Metadata value
        """
        self._loop_metadata[key] = value
        self.logger.debug(f"Loop metadata set: {key}")

    def get_loop_metadata(self, key: str) -> Any:
        """
        Get loop metadata.

        Args:
            key: Metadata key

        Returns:
            Metadata value or None if not found
        """
        return self._loop_metadata.get(key)

    def initialize_loop_state(
        self, total_iterations: int, metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Initialize loop state for execution.

        Args:
            total_iterations: Total number of iterations expected
            metadata: Optional metadata dictionary
        """
        self._current_iteration_index = 0
        self._total_iterations = total_iterations
        self._iteration_status = {}
        self._iteration_results = {}
        self._iteration_contexts = {}
        self._loop_execution_state = "initialized"

        if metadata:
            self._loop_metadata.update(metadata)

        # Initialize all iterations as pending
        for i in range(total_iterations):
            self._iteration_status[i] = "pending"

        self.logger.info(f"Loop state initialized with {total_iterations} iterations")

    def reset_loop_state(self) -> None:
        """Reset loop state for clean slate."""
        self._current_iteration_index = 0
        self._total_iterations = 0
        self._iteration_status = {}
        self._iteration_results = {}
        self._iteration_contexts = {}
        self._loop_metadata = {}
        self._loop_execution_state = "initialized"
        # New architecture state
        self._iteration_outputs = []
        self._exit_output = None
        self._output_routing_state = "pending"
        self._exit_condition_config = {}
        self._exit_condition_state = "not_met"
        self._exit_condition_results = []

        self.logger.debug("Loop state reset")

    def backup_loop_state(self) -> Dict[str, Any]:
        """
        Create a backup of the current loop state.

        Returns:
            Dictionary containing the complete loop state
        """
        backup = {
            "loop_id": self.loop_id,
            "transition_id": self.transition_id,
            "workflow_id": self.workflow_id,
            "current_iteration_index": self._current_iteration_index,
            "total_iterations": self._total_iterations,
            "iteration_status": self._iteration_status.copy(),
            "loop_execution_state": self._loop_execution_state,
            "iteration_results": self._iteration_results.copy(),
            "iteration_contexts": self._iteration_contexts.copy(),
            "loop_metadata": self._loop_metadata.copy(),
            # New architecture state
            "iteration_outputs": self._iteration_outputs.copy(),
            "exit_output": self._exit_output,
            "output_routing_state": self._output_routing_state,
            "exit_condition_config": self._exit_condition_config.copy(),
            "exit_condition_state": self._exit_condition_state,
            "exit_condition_results": self._exit_condition_results.copy(),
        }

        self.logger.debug("Loop state backup created")
        return backup

    def restore_loop_state(self, backup: Dict[str, Any]) -> None:
        """
        Restore loop state from a backup.

        Args:
            backup: Dictionary containing the loop state backup
        """
        self._current_iteration_index = backup.get("current_iteration_index", 0)
        self._total_iterations = backup.get("total_iterations", 0)
        self._iteration_status = backup.get("iteration_status", {})
        self._loop_execution_state = backup.get("loop_execution_state", "initialized")
        self._iteration_results = backup.get("iteration_results", {})
        self._iteration_contexts = backup.get("iteration_contexts", {})
        self._loop_metadata = backup.get("loop_metadata", {})
        # New architecture state
        self._iteration_outputs = backup.get("iteration_outputs", [])
        self._exit_output = backup.get("exit_output", None)
        self._output_routing_state = backup.get("output_routing_state", "pending")
        self._exit_condition_config = backup.get("exit_condition_config", {})
        self._exit_condition_state = backup.get("exit_condition_state", "not_met")
        self._exit_condition_results = backup.get("exit_condition_results", [])

        self.logger.debug("Loop state restored from backup")

    # New architecture methods for output routing and exit conditions

    def store_iteration_output(
        self, iteration_index: int, output_data: Dict[str, Any]
    ) -> None:
        """
        Store iteration output data for tracking.

        Args:
            iteration_index: Index of the iteration
            output_data: Output data sent to connected nodes
        """
        output_entry = {
            "iteration_index": iteration_index,
            "output_data": output_data,
            "timestamp": self._get_current_timestamp(),
            "routing_status": "sent",
        }
        self._iteration_outputs.append(output_entry)
        self.logger.debug(f"Iteration output stored for iteration {iteration_index}")

    def get_iteration_outputs(self) -> List[Dict[str, Any]]:
        """
        Get all iteration outputs.

        Returns:
            List of iteration output entries
        """
        return self._iteration_outputs.copy()

    def store_exit_output(self, exit_data: Dict[str, Any]) -> None:
        """
        Store final exit output data.

        Args:
            exit_data: Final aggregated results and metadata
        """
        self._exit_output = {
            "exit_data": exit_data,
            "timestamp": self._get_current_timestamp(),
            "routing_status": "sent",
        }
        self._output_routing_state = "exit_sent"
        self.logger.debug("Exit output stored")

    def get_exit_output(self) -> Optional[Dict[str, Any]]:
        """
        Get the exit output data.

        Returns:
            Exit output data or None if not set
        """
        return self._exit_output

    def set_output_routing_state(self, state: str) -> None:
        """
        Set the output routing state.

        Args:
            state: New routing state (pending, iteration_active, exit_sent)
        """
        valid_states = ["pending", "iteration_active", "exit_sent"]
        if state not in valid_states:
            raise ValueError(
                f"Invalid output routing state: {state}. Must be one of: {valid_states}"
            )

        self._output_routing_state = state
        self.logger.debug(f"Output routing state changed to: {state}")

    def get_output_routing_state(self) -> str:
        """
        Get the current output routing state.

        Returns:
            Current output routing state
        """
        return self._output_routing_state

    def set_exit_condition_config(self, config: Dict[str, Any]) -> None:
        """
        Set the exit condition configuration.

        Args:
            config: Exit condition configuration from loop_config
        """
        self._exit_condition_config = config.copy()
        self.logger.debug("Exit condition config set")

    def get_exit_condition_config(self) -> Dict[str, Any]:
        """
        Get the exit condition configuration.

        Returns:
            Exit condition configuration
        """
        return self._exit_condition_config.copy()

    def set_exit_condition_state(self, state: str) -> None:
        """
        Set the exit condition evaluation state.

        Args:
            state: New state (not_met, evaluating, met)
        """
        valid_states = ["not_met", "evaluating", "met"]
        if state not in valid_states:
            raise ValueError(
                f"Invalid exit condition state: {state}. Must be one of: {valid_states}"
            )

        self._exit_condition_state = state
        self.logger.debug(f"Exit condition state changed to: {state}")

    def get_exit_condition_state(self) -> str:
        """
        Get the current exit condition state.

        Returns:
            Current exit condition state
        """
        return self._exit_condition_state

    def add_exit_condition_result(
        self, iteration_index: int, result: Dict[str, Any]
    ) -> None:
        """
        Add an exit condition evaluation result.

        Args:
            iteration_index: Index of the iteration
            result: Exit condition evaluation result
        """
        result_entry = {
            "iteration_index": iteration_index,
            "result": result,
            "timestamp": self._get_current_timestamp(),
        }
        self._exit_condition_results.append(result_entry)
        self.logger.debug(
            f"Exit condition result added for iteration {iteration_index}"
        )

    def get_exit_condition_results(self) -> List[Dict[str, Any]]:
        """
        Get all exit condition evaluation results.

        Returns:
            List of exit condition results
        """
        return self._exit_condition_results.copy()

    def _get_current_timestamp(self) -> float:
        """
        Get current timestamp.

        Returns:
            Current timestamp as float
        """
        import time

        return time.time()

    def get_execution_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the loop execution state.

        Returns:
            Dictionary containing execution summary
        """
        completed_count = sum(
            1 for status in self._iteration_status.values() if status == "completed"
        )
        failed_count = sum(
            1 for status in self._iteration_status.values() if status == "failed"
        )
        running_count = sum(
            1 for status in self._iteration_status.values() if status == "running"
        )
        pending_count = sum(
            1 for status in self._iteration_status.values() if status == "pending"
        )

        summary = {
            "loop_id": self.loop_id,
            "transition_id": self.transition_id,
            "workflow_id": self.workflow_id,
            "execution_state": self._loop_execution_state,
            "current_iteration": self._current_iteration_index,
            "total_iterations": self._total_iterations,
            "completed_iterations": completed_count,
            "failed_iterations": failed_count,
            "running_iterations": running_count,
            "pending_iterations": pending_count,
            "progress_percentage": (
                (completed_count / self._total_iterations * 100)
                if self._total_iterations > 0
                else 0
            ),
        }

        return summary

    def is_loop_complete(self) -> bool:
        """
        Check if the loop execution is complete.

        Returns:
            True if all iterations are completed or failed, False otherwise
        """
        if not self._iteration_status:
            return False

        incomplete_statuses = ["pending", "running"]
        for status in self._iteration_status.values():
            if status in incomplete_statuses:
                return False

        return True

    def serialize_state(self) -> str:
        """
        Serialize loop state to JSON string.

        Returns:
            JSON string representation of the loop state
        """
        state_data = self.backup_loop_state()
        return json.dumps(state_data, default=str)

    def deserialize_state(self, state_json: str) -> None:
        """
        Deserialize loop state from JSON string.

        Args:
            state_json: JSON string containing loop state
        """
        state_data = json.loads(state_json)
        self.restore_loop_state(state_data)
