#!/usr/bin/env python3
"""
Quick test to verify the loop body transition auto-detection fix.
"""

import asyncio
import sys
import os

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core_.transition_handler import TransitionHandler
from app.utils.enhanced_logger import get_logger

logger = get_logger("LoopFixTest")

async def test_loop_body_auto_detection():
    """Test that loop body transitions are auto-detected and added to loop config."""

    # Create a mock transition handler with minimal dependencies
    class MockTransitionHandler:
        def __init__(self):
            self.logger = logger

        def _auto_detect_loop_body_transitions(self, output_data_configs):
            """Copy of the auto-detection logic from TransitionHandler."""
            loop_body_transitions = []

            for config in output_data_configs:
                to_transition_id = config.get("to_transition_id")
                if not to_transition_id:
                    continue

                # Check handle mappings to determine if this is a loop body transition
                output_handle_registry = config.get("output_handle_registry", {})
                handle_mappings = output_handle_registry.get("handle_mappings", [])

                is_loop_body = False
                is_exit = False

                for mapping in handle_mappings:
                    result_path = mapping.get("result_path", "").lower()
                    handle_name = mapping.get("handle_name", "").lower()

                    # Check for loop body indicators
                    if (
                        "current_item" in result_path or
                        "current_item" in handle_name or
                        "iteration" in result_path or
                        "item" in handle_name
                    ):
                        is_loop_body = True

                    # Check for exit indicators
                    if (
                        "final" in result_path or
                        "aggregated" in result_path or
                        "exit" in result_path or
                        "final_results" in handle_name or
                        "aggregated_results" in handle_name
                    ):
                        is_exit = True

                # If it looks like a loop body transition and not an exit transition
                if is_loop_body and not is_exit:
                    loop_body_transitions.append(to_transition_id)
                    self.logger.debug(
                        f"🔍 Auto-detected loop body transition: {to_transition_id} "
                        f"(has current_item/iteration indicators)"
                    )
                elif is_exit:
                    self.logger.debug(
                        f"🔍 Detected exit transition: {to_transition_id} "
                        f"(has final/aggregated indicators)"
                    )

            return loop_body_transitions

    transition_handler = MockTransitionHandler()

    # Mock output data configs that should trigger auto-detection
    output_data_configs = [
        {
            "to_transition_id": "transition-CombineTextComponent-1750769520925",
            "target_node_id": "Combine Text",
            "data_type": "string",
            "output_handle_registry": {
                "handle_mappings": [
                    {
                        "handle_id": "current_item",
                        "result_path": "current_item",
                        "edge_id": "reactflow__edge-LoopNode-1750775661045current_item-CombineTextComponent-1750769520925main_input"
                    }
                ]
            }
        },
        {
            "to_transition_id": "transition-MessageToDataComponent-1750769557490",
            "target_node_id": "Message To Data",
            "data_type": "string",
            "output_handle_registry": {
                "handle_mappings": [
                    {
                        "handle_id": "final_results",
                        "result_path": "final_results",
                        "edge_id": "reactflow__edge-LoopNode-1750775661045final_results-MessageToDataComponent-1750769557490input_message"
                    }
                ]
            }
        }
    ]
    
    # Test auto-detection
    loop_body_transitions = transition_handler._auto_detect_loop_body_transitions(output_data_configs)
    
    print(f"🔍 Auto-detected loop body transitions: {loop_body_transitions}")
    
    # Verify the correct transition was detected
    expected_loop_body = "transition-CombineTextComponent-1750769520925"
    expected_exit = "transition-MessageToDataComponent-1750769557490"
    
    if expected_loop_body in loop_body_transitions:
        print(f"✅ SUCCESS: Loop body transition correctly detected: {expected_loop_body}")
    else:
        print(f"❌ FAILED: Loop body transition not detected. Expected: {expected_loop_body}")
        return False
    
    if expected_exit not in loop_body_transitions:
        print(f"✅ SUCCESS: Exit transition correctly excluded: {expected_exit}")
    else:
        print(f"❌ FAILED: Exit transition incorrectly detected as loop body: {expected_exit}")
        return False
    
    # Test that the loop config would be enhanced
    original_loop_config = {
        "iteration_behavior": "independent",
        "iteration_source": {"number_range": {"start": 1, "end": 10}, "step": 2},
        "exit_condition": {"condition_type": "all_items_processed"}
    }
    
    # Simulate the enhancement that should happen in transition handler
    enhanced_config = original_loop_config.copy()
    if loop_body_transitions:
        enhanced_config["loop_body_transitions"] = loop_body_transitions
    
    print(f"📝 Original config: {original_loop_config}")
    print(f"🔧 Enhanced config: {enhanced_config}")
    
    if "loop_body_transitions" in enhanced_config:
        print("✅ SUCCESS: Loop config would be enhanced with loop body transitions")
        return True
    else:
        print("❌ FAILED: Loop config would not be enhanced")
        return False

if __name__ == "__main__":
    print("🧪 Testing Loop Body Transition Auto-Detection Fix...")
    
    result = asyncio.run(test_loop_body_auto_detection())
    
    if result:
        print("\n🎉 ALL TESTS PASSED! The fix should work correctly.")
        sys.exit(0)
    else:
        print("\n💥 TESTS FAILED! The fix needs more work.")
        sys.exit(1)
