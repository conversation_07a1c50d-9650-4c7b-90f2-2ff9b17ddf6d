#!/usr/bin/env python3
"""
Integration test to verify the complete loop execution flow with the fix.
"""

import asyncio
import sys
import os
import json
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from app.core_.transition_handler import <PERSON>ionHand<PERSON>
from app.core_.state_manager import WorkflowStateManager
from app.utils.enhanced_logger import get_logger
from unittest.mock import Mock, AsyncMock

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = get_logger("LoopIntegrationTest")

async def test_complete_loop_execution():
    """Test the complete loop execution flow with auto-detected loop body transitions."""
    
    logger.info("🧪 Starting Complete Loop Execution Integration Test...")
    
    # Create mock dependencies
    mock_workflow_utils = Mock()
    mock_node_executor = AsyncMock()
    mock_tool_executor = AsyncMock()
    mock_agent_executor = AsyncMock()

    # Mock the node executor to return a simple result
    mock_node_executor.execute_node.return_value = {
        "success": True,
        "result": "Processed item successfully",
        "output_data": {"processed_text": "Item processed"}
    }

    # Create state manager
    state_manager = WorkflowStateManager()

    # Test workflow configuration (similar to the one from the logs)
    workflow_config = {
        "nodes": {
            "LoopNode-1750863512419": {
                "id": "LoopNode-1750863512419",
                "type": "LoopNode",
                "data": {
                    "loop_config": {
                        "iteration_behavior": "independent",
                        "iteration_source": {
                            "number_range": {"start": 1, "end": 3},
                            "step": 1
                        },
                        "exit_condition": {
                            "condition_type": "all_items_processed"
                        },
                        "iteration_settings": {
                            "parallel_execution": False,
                            "max_concurrent": 3,
                            "preserve_order": True,
                            "iteration_timeout": 60
                        },
                        "result_aggregation": {
                            "aggregation_type": "collect_all",
                            "include_metadata": True
                        },
                        "loop_body_configuration": {
                            "entry_transitions": [],
                            "exit_transitions": [],
                            "chain_completion_detection": "explicit_exit_transitions"
                        }
                    }
                }
            },
            "CombineTextComponent-1750769520925": {
                "id": "CombineTextComponent-1750769520925",
                "type": "CombineTextComponent",
                "data": {}
            },
            "MessageToDataComponent-1750769557490": {
                "id": "MessageToDataComponent-1750769557490",
                "type": "MessageToDataComponent",
                "data": {}
            }
        },
        "transitions": {
            "transition-LoopNode-1750863512419": {
                "id": "transition-LoopNode-1750863512419",
                "source_node_id": "LoopNode-1750863512419",
                "execution_type": "LoopNode",
                "loop_config": {
                    "iteration_behavior": "independent",
                    "iteration_source": {
                        "number_range": {"start": 1, "end": 3},
                        "step": 1
                    },
                    "exit_condition": {
                        "condition_type": "all_items_processed"
                    }
                },
                "node_info": {
                    "node_id": "LoopNode-1750863512419",
                    "output_data": [
                        {
                            "to_transition_id": "transition-CombineTextComponent-1750769520925",
                            "target_node_id": "CombineTextComponent-1750769520925",
                            "data_type": "string",
                            "output_handle_registry": {
                                "handle_mappings": [{
                                    "handle_id": "current_item",
                                    "result_path": "current_item",
                                    "edge_id": "reactflow__edge-LoopNode-1750863512419current_item-CombineTextComponent-1750769520925main_input"
                                }]
                            }
                        },
                        {
                            "to_transition_id": "transition-MessageToDataComponent-1750769557490",
                            "target_node_id": "MessageToDataComponent-1750769557490",
                            "data_type": "string",
                            "output_handle_registry": {
                                "handle_mappings": [{
                                    "handle_id": "final_results",
                                    "result_path": "final_results",
                                    "edge_id": "reactflow__edge-LoopNode-1750863512419final_results-MessageToDataComponent-1750769557490input_message"
                                }]
                            }
                        }
                    ]
                }
            },
            "transition-CombineTextComponent-1750769520925": {
                "id": "transition-CombineTextComponent-1750769520925",
                "source_node_id": "CombineTextComponent-1750769520925",
                "execution_type": "Components"
            },
            "transition-MessageToDataComponent-1750769557490": {
                "id": "transition-MessageToDataComponent-1750769557490",
                "source_node_id": "MessageToDataComponent-1750769557490",
                "execution_type": "Components"
            }
        }
    }
    

    
    # Create transition handler with the workflow data
    transitions_by_id = workflow_config["transitions"]
    nodes = workflow_config["nodes"]
    dependency_map = {}  # Empty for this test

    transition_handler = TransitionHandler(
        state_manager=state_manager,
        transitions_by_id=transitions_by_id,
        nodes=nodes,
        dependency_map=dependency_map,
        workflow_utils=mock_workflow_utils,
        tool_executor=mock_tool_executor,
        node_executor=mock_node_executor,
        agent_executor=mock_agent_executor
    )

    # Initialize workflow state
    workflow_id = "test-workflow-123"
    state_manager.initialize_workflow("transition-LoopNode-1750863512419")

    # Test the loop transition execution
    loop_transition = workflow_config["transitions"]["transition-LoopNode-1750863512419"]

    try:
        logger.info("🔄 Executing loop transition...")

        # Execute the loop transition
        result = await transition_handler._execute_transition_with_tracking(
            transition=loop_transition
        )
        
        logger.info(f"✅ Loop execution completed with result: {result}")
        
        # Verify that the loop body transitions were auto-detected and executed
        if result and result.get("success"):
            logger.info("🎉 SUCCESS: Loop execution completed successfully!")
            logger.info(f"📊 Final result: {json.dumps(result, indent=2)}")
        else:
            logger.error(f"❌ FAILURE: Loop execution failed with result: {result}")
            
    except Exception as e:
        logger.error(f"❌ FAILURE: Loop execution threw exception: {str(e)}")
        import traceback
        traceback.print_exc()
    
    logger.info("🏁 Integration test completed.")

if __name__ == "__main__":
    asyncio.run(test_complete_loop_execution())
