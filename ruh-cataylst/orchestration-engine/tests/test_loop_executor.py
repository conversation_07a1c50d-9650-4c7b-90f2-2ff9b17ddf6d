import pytest
from unittest.mock import AsyncMock, Mock
from app.core_.loop_executor import LoopExecutor


class TestLoopExecutor:
    """
    Test suite for LoopExecutor functionality.
    Tests loop configuration parsing, validation, and basic execution flow.
    """

    @pytest.fixture
    def mock_dependencies(self):
        """Create mock dependencies for LoopExecutor."""
        return {
            "state_manager": <PERSON><PERSON>(),
            "workflow_utils": <PERSON><PERSON>(),
            "result_callback": AsyncMock(),
            "transitions_by_id": {
                "body-transition-1": {"id": "body-transition-1", "type": "standard"},
                "body-transition-2": {"id": "body-transition-2", "type": "standard"},
            },
            "nodes": {
                "test-node": {"id": "test-node", "server_tools": []},
            },
            "transition_handler": <PERSON><PERSON>(),
            "user_id": "test-user-123",
        }

    @pytest.fixture
    def loop_executor(self, mock_dependencies):
        """Create a LoopExecutor instance with mock dependencies."""
        return LoopExecutor(**mock_dependencies)

    def test_loop_executor_initialization(self, loop_executor):
        """Test that LoopExecutor initializes correctly."""
        assert loop_executor.state_manager is not None
        assert loop_executor.workflow_utils is not None
        assert loop_executor.result_callback is not None
        assert loop_executor.transitions_by_id is not None
        assert loop_executor.nodes is not None
        assert loop_executor.transition_handler is not None
        assert loop_executor.user_id == "test-user-123"
        assert loop_executor.current_loop_config is None
        assert loop_executor.current_iteration_data == []
        assert loop_executor.iteration_results == {}
        assert loop_executor.loop_context == {}

    def test_parse_loop_config_valid(self, loop_executor):
        """Test parsing of valid loop configuration."""
        loop_config = {
            "loop_type": "context_independent",
            "aggregation_type": "aggregating",
            "iteration_source": {
                "type": "list",
                "data": ["item1", "item2", "item3"]
            },
            "loop_body_transitions": ["body-transition-1"],
            "exit_transition": "exit-transition",
            "concurrency": {
                "enabled": True,
                "max_concurrent": 3,
                "preserve_order": True
            }
        }

        parsed = loop_executor.parse_loop_config(loop_config)

        assert parsed["loop_type"] == "context_independent"
        assert parsed["aggregation_type"] == "aggregating"
        assert parsed["iteration_source"]["type"] == "list"
        assert parsed["iteration_source"]["data"] == ["item1", "item2", "item3"]
        assert parsed["loop_body_transitions"] == ["body-transition-1"]
        assert parsed["exit_transition"] == "exit-transition"
        assert parsed["concurrency"]["enabled"] is True
        assert parsed["concurrency"]["max_concurrent"] == 3

    def test_parse_loop_config_missing(self, loop_executor):
        """Test parsing with missing loop configuration."""
        with pytest.raises(ValueError, match="Loop configuration is required"):
            loop_executor.parse_loop_config(None)

    def test_validate_loop_config_valid(self, loop_executor):
        """Test validation of valid loop configuration."""
        config = {
            "loop_type": "context_preserving",
            "aggregation_type": "non_aggregating",
            "iteration_source": {
                "type": "range",
                "data": {"start": 0, "stop": 5, "step": 1}
            },
            "loop_body_transitions": ["body-transition-1", "body-transition-2"]
        }

        # Should not raise any exception
        loop_executor.validate_loop_config(config)

    def test_validate_loop_config_missing_required_field(self, loop_executor):
        """Test validation with missing required field."""
        config = {
            "loop_type": "context_independent",
            # Missing aggregation_type
            "iteration_source": {
                "type": "list",
                "data": ["item1"]
            },
            "loop_body_transitions": ["body-transition-1"]
        }

        with pytest.raises(ValueError, match="Required loop configuration field missing: aggregation_type"):
            loop_executor.validate_loop_config(config)

    def test_validate_loop_config_invalid_loop_type(self, loop_executor):
        """Test validation with invalid loop type."""
        config = {
            "loop_type": "invalid_type",
            "aggregation_type": "aggregating",
            "iteration_source": {
                "type": "list",
                "data": ["item1"]
            },
            "loop_body_transitions": ["body-transition-1"]
        }

        with pytest.raises(ValueError, match="Invalid loop_type: invalid_type"):
            loop_executor.validate_loop_config(config)

    def test_validate_loop_config_invalid_aggregation_type(self, loop_executor):
        """Test validation with invalid aggregation type."""
        config = {
            "loop_type": "context_independent",
            "aggregation_type": "invalid_aggregation",
            "iteration_source": {
                "type": "list",
                "data": ["item1"]
            },
            "loop_body_transitions": ["body-transition-1"]
        }

        with pytest.raises(ValueError, match="Invalid aggregation_type: invalid_aggregation"):
            loop_executor.validate_loop_config(config)

    def test_validate_loop_config_invalid_iteration_source_type(self, loop_executor):
        """Test validation with invalid iteration source type."""
        config = {
            "loop_type": "context_independent",
            "aggregation_type": "aggregating",
            "iteration_source": {
                "type": "invalid_source",
                "data": ["item1"]
            },
            "loop_body_transitions": ["body-transition-1"]
        }

        with pytest.raises(ValueError, match="Invalid iteration_source.type: invalid_source"):
            loop_executor.validate_loop_config(config)

    def test_validate_loop_config_nonexistent_transition(self, loop_executor):
        """Test validation with non-existent loop body transition."""
        config = {
            "loop_type": "context_independent",
            "aggregation_type": "aggregating",
            "iteration_source": {
                "type": "list",
                "data": ["item1"]
            },
            "loop_body_transitions": ["nonexistent-transition"]
        }

        with pytest.raises(ValueError, match="Loop body transition not found: nonexistent-transition"):
            loop_executor.validate_loop_config(config)

    def test_prepare_iteration_data_list(self, loop_executor):
        """Test preparation of iteration data from list source."""
        loop_executor.current_loop_config = {
            "iteration_source": {
                "type": "list",
                "data": ["apple", "banana", "cherry"]
            }
        }

        loop_executor.prepare_iteration_data()

        expected_data = [(0, "apple"), (1, "banana"), (2, "cherry")]
        assert loop_executor.current_iteration_data == expected_data

    def test_prepare_iteration_data_range(self, loop_executor):
        """Test preparation of iteration data from range source."""
        loop_executor.current_loop_config = {
            "iteration_source": {
                "type": "range",
                "data": {"start": 1, "stop": 4, "step": 1}
            }
        }

        loop_executor.prepare_iteration_data()

        expected_data = [(0, 1), (1, 2), (2, 3)]
        assert loop_executor.current_iteration_data == expected_data

    def test_prepare_iteration_data_invalid_list(self, loop_executor):
        """Test preparation with invalid list data."""
        loop_executor.current_loop_config = {
            "iteration_source": {
                "type": "list",
                "data": "not_a_list"
            }
        }

        with pytest.raises(ValueError, match="List iteration source data must be an array"):
            loop_executor.prepare_iteration_data()

    def test_create_iteration_context_independent(self, loop_executor):
        """Test creation of iteration context for context-independent loops."""
        loop_executor.current_loop_config = {"loop_type": "context_independent"}
        loop_executor.loop_context = {"total_iterations": 3}

        context = loop_executor.create_iteration_context(1, "test_item")

        assert context["current_item"] == "test_item"
        assert context["iteration_index"] == 1
        assert context["total_iterations"] == 3
        assert "previous_results" not in context

    def test_create_iteration_context_preserving(self, loop_executor):
        """Test creation of iteration context for context-preserving loops."""
        loop_executor.current_loop_config = {"loop_type": "context_preserving"}
        loop_executor.loop_context = {"total_iterations": 3}
        loop_executor.iteration_results = {0: "result_0"}

        context = loop_executor.create_iteration_context(1, "test_item")

        assert context["current_item"] == "test_item"
        assert context["iteration_index"] == 1
        assert context["total_iterations"] == 3
        assert context["previous_results"] == ["result_0"]

    def test_aggregate_results_aggregating(self, loop_executor):
        """Test result aggregation for aggregating loops."""
        loop_executor.current_loop_config = {"aggregation_type": "aggregating"}
        loop_executor.iteration_results = {0: "result_0", 2: "result_2", 1: "result_1"}

        results = loop_executor.aggregate_results()

        assert results == ["result_0", "result_1", "result_2"]

    def test_aggregate_results_non_aggregating(self, loop_executor):
        """Test result aggregation for non-aggregating loops."""
        loop_executor.current_loop_config = {"aggregation_type": "non_aggregating"}
        loop_executor.iteration_results = {0: "result_0", 1: "result_1", 2: "result_2"}

        results = loop_executor.aggregate_results()

        assert results == ["result_2"]  # Only the last result

    def test_aggregate_results_empty(self, loop_executor):
        """Test result aggregation with empty results."""
        loop_executor.current_loop_config = {"aggregation_type": "aggregating"}
        loop_executor.iteration_results = {}

        results = loop_executor.aggregate_results()

        assert results == []

    @pytest.mark.asyncio
    async def test_initialize_loop_state(self, loop_executor):
        """Test loop state initialization."""
        await loop_executor.initialize_loop_state("test-transition-123")

        assert loop_executor.iteration_results == {}
        assert loop_executor.loop_context["transition_id"] == "test-transition-123"
        assert loop_executor.loop_context["total_iterations"] == 0
        assert loop_executor.loop_context["completed_iterations"] == 0
        assert loop_executor.loop_context["failed_iterations"] == 0

    def test_reset_loop_state(self, loop_executor):
        """Test loop state reset."""
        # Set some state
        loop_executor.current_loop_config = {"test": "config"}
        loop_executor.current_iteration_data = [(0, "item")]
        loop_executor.iteration_results = {0: "result"}
        loop_executor.loop_context = {"test": "context"}

        loop_executor.reset_loop_state()

        assert loop_executor.current_loop_config is None
        assert loop_executor.current_iteration_data == []
        assert loop_executor.iteration_results == {}
        assert loop_executor.loop_context == {}
