import pytest
import json
from unittest.mock import patch, mock_open
from app.utils.helper_functions import (
    load_schema,
    fix_invalid_escapes,
    format_execution_result,
)


class TestHelperFunctions:
    """
    Test suite for helper functions.
    Tests schema loading, JSON string fixing, and execution result formatting.
    """

    def test_load_schema_valid_json(self):
        """
        Test loading a valid JSON schema from file.
        """
        mock_schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
        }
        mock_json = json.dumps(mock_schema)

        with patch("builtins.open", mock_open(read_data=mock_json)):
            result = load_schema("dummy/path.json")
            assert result == mock_schema
            assert isinstance(result, dict)
            assert "properties" in result

    def test_load_schema_invalid_json(self):
        """
        Test loading an invalid JSON schema raises JSONDecodeError.
        """
        with patch("builtins.open", mock_open(read_data="invalid json")):
            with pytest.raises(json.JSONDecodeError):
                load_schema("dummy/path.json")

    def test_load_schema_file_not_found(self):
        """
        Test attempting to load schema from non-existent file.
        """
        with pytest.raises(FileNotFoundError):
            load_schema("nonexistent/path.json")

    def test_fix_invalid_escapes_basic(self):
        """
        Test fixing basic invalid escape sequences in JSON string.
        """
        input_str = r'{"key": "value with \invalid escape"}'
        expected = r'{"key": "value with \\invalid escape"}'
        result = fix_invalid_escapes(input_str)
        assert result == expected

    def test_fix_invalid_escapes_valid_escapes(self):
        """
        Test that valid escape sequences remain unchanged.
        """
        valid_escapes = r'{"key": "\\\"\/\b\f\n\r\t"}'
        result = fix_invalid_escapes(valid_escapes)
        assert result == valid_escapes

    def test_fix_invalid_escapes_mixed(self):
        """
        Test fixing string with both valid and invalid escapes.
        """
        input_str = r'{"key": "\valid \invalid \n \escape"}'
        expected = r'{"key": "\\valid \\invalid \n \\escape"}'
        result = fix_invalid_escapes(input_str)
        assert result == expected

    def test_format_execution_result_empty_input(self):
        """
        Test formatting empty execution result.
        """
        output_schema = {"type": "object", "properties": {}}
        result = format_execution_result(output_schema, [])
        assert isinstance(result, list)
        assert len(result) == 0

    def test_format_execution_result_basic(self):
        """
        Test basic execution result formatting with simple schema.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "test_field",
                    "data_type": {"type": "string", "description": "A test field"},
                }
            ]
        }
        execution_result = [{"test_field": "test value"}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "test_field"
        assert result[0]["data"] == "test value"
        assert result[0]["data_type"] == "string"
        assert result[0]["semantic_type"] == "string"  # default

    def test_format_execution_result_unknown_property(self):
        """
        Test formatting result with unknown property.
        """
        output_schema = {"type": "object", "properties": {}}
        execution_result = [{"unknown_field": "test value"}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "unknown_field"
        assert result[0]["data"] == "test value"
        assert result[0]["data_type"] == "unknown"

    def test_format_execution_result_multiple_properties(self):
        """
        Test formatting result with multiple properties.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "string_field",
                    "data_type": {"type": "string", "description": "A string field"},
                },
                {
                    "field_name": "number_field",
                    "data_type": {"type": "number", "description": "A number field"},
                },
            ]
        }
        execution_result = [{"string_field": "test string", "number_field": 42}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 2

        # Find and verify string field
        string_item = next(
            item for item in result if item["property_name"] == "string_field"
        )
        assert string_item["data"] == "test string"
        assert string_item["data_type"] == "string"
        assert string_item["semantic_type"] == "string"  # default

        # Find and verify number field
        number_item = next(
            item for item in result if item["property_name"] == "number_field"
        )
        assert number_item["data"] == 42
        assert number_item["data_type"] == "number"
        assert number_item["semantic_type"] == "number"  # data-driven detection

    def test_format_execution_result_nested_objects(self):
        """
        Test formatting result with nested object structures.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "nested_obj",
                    "data_type": {
                        "type": "object",
                        "description": "A nested object",
                        "properties": {"inner_field": {"type": "string"}},
                    },
                }
            ]
        }
        execution_result = [{"nested_obj": {"inner_field": "inner value"}}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "nested_obj"
        assert isinstance(
            result[0]["data"], list
        )  # objects are formatted as lists of items
        assert result[0]["data_type"] == "object"
        assert result[0]["semantic_type"] == "object"  # default for objects

    def test_format_execution_result_array_type(self):
        """
        Test formatting result with array type fields.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "array_field",
                    "data_type": {
                        "type": "array",
                        "description": "An array field",
                        "items": {"type": "string"},
                    },
                }
            ]
        }
        execution_result = [{"array_field": ["item1", "item2", "item3"]}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "array_field"
        assert isinstance(result[0]["data"], list)
        # Array data is formatted as list of items with semantic types
        assert len(result[0]["data"]) == 3
        assert result[0]["data_type"] == "array"
        assert result[0]["semantic_type"] == "array"  # default for arrays

    def test_format_execution_result_with_semantic_types(self):
        """
        Test formatting result with semantic type information.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "user_email",
                    "data_type": {
                        "type": "string",
                        "format": "email",
                        "description": "User email address",
                    },
                },
                {
                    "field_name": "website_url",
                    "data_type": {
                        "type": "string",
                        "format": "url",
                        "description": "Website URL",
                    },
                },
                {
                    "field_name": "created_at",
                    "data_type": {
                        "type": "string",
                        "format": "datetime",
                        "description": "Creation timestamp",
                    },
                },
            ]
        }
        execution_result = [
            {
                "user_email": "<EMAIL>",
                "website_url": "https://example.com",
                "created_at": "2024-01-15T10:30:00Z",
            }
        ]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 3

        # Find and verify email field
        email_item = next(
            item for item in result if item["property_name"] == "user_email"
        )
        assert email_item["data"] == "<EMAIL>"
        assert email_item["data_type"] == "string"
        assert email_item["semantic_type"] == "email"

        # Find and verify URL field
        url_item = next(
            item for item in result if item["property_name"] == "website_url"
        )
        assert url_item["data"] == "https://example.com"
        assert url_item["data_type"] == "string"
        assert url_item["semantic_type"] == "url"

        # Find and verify datetime field
        datetime_item = next(
            item for item in result if item["property_name"] == "created_at"
        )
        assert datetime_item["data"] == "2024-01-15T10:30:00Z"
        assert datetime_item["data_type"] == "string"
        assert datetime_item["semantic_type"] == "datetime"

    def test_format_execution_result_default_semantic_type(self):
        """
        Test formatting result with default semantic type when format is missing.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "description",
                    "data_type": {
                        "type": "string",
                        "description": "A description field",
                    },
                }
            ]
        }
        execution_result = [{"description": "test description"}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "description"
        assert result[0]["data"] == "test description"
        assert result[0]["data_type"] == "string"
        assert result[0]["semantic_type"] == "string"  # default

    def test_format_execution_result_unknown_property_semantic_type(self):
        """
        Test formatting result with unknown property includes default semantic type.
        """
        output_schema = {"predefined_fields": []}
        execution_result = [{"unknown_field": "test value"}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "unknown_field"
        assert result[0]["data"] == "test value"
        assert result[0]["data_type"] == "unknown"
        assert result[0]["semantic_type"] == "string"  # default for unknown

    def test_format_execution_result_array_with_semantic_types(self):
        """
        Test formatting result with array containing semantic type information.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "email_list",
                    "data_type": {
                        "type": "array",
                        "items": {"type": "string", "format": "email"},
                        "description": "List of email addresses",
                    },
                }
            ]
        }
        execution_result = [{"email_list": ["<EMAIL>", "<EMAIL>"]}]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "email_list"
        assert result[0]["data_type"] == "array"
        assert result[0]["semantic_type"] == "array"  # data-driven detection returns "array"

        # Check array items have semantic types
        array_data = result[0]["data"]
        assert isinstance(array_data, list)
        assert len(array_data) == 2
        assert array_data[0]["semantic_type"] == "email"
        assert array_data[1]["semantic_type"] == "email"

    def test_format_execution_result_object_with_semantic_types(self):
        """
        Test formatting result with object containing semantic type information.
        """
        output_schema = {
            "predefined_fields": [
                {
                    "field_name": "user_profile",
                    "data_type": {
                        "type": "object",
                        "format": "user_data",
                        "properties": {
                            "email": {"type": "string", "format": "email"},
                            "website": {"type": "string", "format": "url"},
                        },
                        "description": "User profile object",
                    },
                }
            ]
        }
        execution_result = [
            {
                "user_profile": {
                    "email": "<EMAIL>",
                    "website": "https://user.example.com",
                }
            }
        ]

        result = format_execution_result(output_schema, execution_result)

        assert isinstance(result, list)
        assert len(result) == 1
        assert result[0]["property_name"] == "user_profile"
        assert result[0]["data_type"] == "object"
        assert result[0]["semantic_type"] == "object"  # data-driven detection returns "object"
