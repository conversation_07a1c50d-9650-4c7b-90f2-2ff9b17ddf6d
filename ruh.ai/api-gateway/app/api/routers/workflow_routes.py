from fastapi import APIRouter, Depends, HTTPException, Query
from app.services.user_service import UserServiceClient
from app.services.workflow_service import WorkflowServiceClient
import json
from app.utils.parse_error import parse_error
from app.schemas.workflow import (
    PaginatedWorkflowResponse,
    PaginationMetadata,
    ToggleVisibilityResponseAPI,
    UpdateWorkflowResponse,
    WorkflowCategoryEnum,
    WorkflowPatchPayload,
    WorkflowResponse,
    WorkflowCreate,
    WorkflowInDB,
    CreateWorkflowResponse,
    DeleteWorkflowResponse,
    WorkflowStatusEnum,
    WorkflowVisibilityEnum,
    WorkflowsByIdsRequest as WorkflowsByIdsRequestPayload,
    WorkflowsByIdsApiResponse,
    ListWorkflowVersionsResponse,
    GetWorkflowVersionResponse,
    SwitchWorkflowVersionResponse,
    CreateWorkflowVersionRequest,
    CreateWorkflowVersionResponse,
    WorkflowVersionInDB,
)
from google.protobuf.json_format import MessageToDict
from app.core.auth_guard import role_required
from app.core.security import validate_agent_platform_auth_key, validate_server_auth_key
from typing import Optional, Dict, Any


workflow_router = APIRouter(prefix="/workflows", tags=["workflows"])

workflow_service = WorkflowServiceClient()
user_service = UserServiceClient()


# Helper functions to reduce code duplication
def parse_json_tags(workflow_dict: Dict[str, Any]) -> Dict[str, Any]:
    """Parse JSON string tags into Python dictionaries."""
    if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
        try:
            workflow_dict["tags"] = json.loads(workflow_dict["tags"])
        except json.JSONDecodeError:
            workflow_dict["tags"] = None
    return workflow_dict


async def validate_user(user_id: str) -> Dict[str, Any]:
    """Validate a user and return user details."""
    validate_response = await user_service.validate_user(user_id)
    if not validate_response["success"]:
        raise HTTPException(status_code=400, detail=validate_response["message"])
    return validate_response["user"]


def handle_grpc_response_status(response, success_code=200):
    """Handle common gRPC response status code mapping."""
    if not response.success:
        http_status_code = 400
        if "not found" in response.message.lower():
            http_status_code = 404
        elif "permission denied" in response.message.lower():
            http_status_code = 403
        elif "invalid argument" in response.message.lower():
            http_status_code = 400
        else:
            http_status_code = 500
        raise HTTPException(status_code=http_status_code, detail=response.message)
    return success_code


def prepare_workflow_dict(raw_dict):
    # Decode nested fields
    if "tags" in raw_dict and isinstance(raw_dict["tags"], str):
        try:
            raw_dict["tags"] = json.loads(raw_dict["tags"])
        except json.JSONDecodeError:
            raw_dict["tags"] = None

    if "start_nodes" in raw_dict:
        raw_dict["start_nodes"] = [
            json.loads(n) if isinstance(n, str) else n for n in raw_dict["start_nodes"]
        ]
    if "available_nodes" in raw_dict:
        raw_dict["available_nodes"] = [
            json.loads(n) if isinstance(n, str) else n for n in raw_dict["available_nodes"]
        ]

    return raw_dict


@workflow_router.post("", response_model=CreateWorkflowResponse)
async def create_workflow(
    workflow_data: WorkflowCreate, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Create a new workflow with automatic versioning.

    This endpoint creates a new workflow with the provided data and automatically creates
    a v1.0.0 version. The workflow is associated with the authenticated user who makes the request.

    ## Request Body
    - **name**: The name of the workflow
    - **workflow_data**: The workflow definition data
    - **start_node_data**: List of start node IDs

    ## Response
    Returns a success message and the ID of the created workflow with version information.

    ## Errors
    - 400: Bad request (invalid input, validation failed)
    - 500: Server error
    """
    try:
        # Validate user and get user details
        user_id = current_user.get("user_id")
        role = current_user.get("role")

        # user_id = "fce79072-a235-4127-ac5b-b5b1709a8077"
        # role = "user"

        response = await workflow_service.create_workflow(
            name=workflow_data.name,
            workflow_data=workflow_data.workflow_data,
            owner_details={"id": user_id},
            start_nodes=workflow_data.start_node_data,
            owner_type=role,
            description=getattr(workflow_data, "description", ""),
            visibility="private",  # Default to private
            category=getattr(workflow_data, "category", None),
            tags=getattr(workflow_data, "tags", None),
            status="active",  # Default to active
        )

        handle_grpc_response_status(response)

        workflow_dict = MessageToDict(response.workflow, preserving_proto_field_name=True)
        workflow_dict = parse_json_tags(workflow_dict)

        workflow_dict = prepare_workflow_dict(workflow_dict)

        print(
            f"[DEBUG] Available nodes converted to dict: {workflow_dict.get('available_nodes', [])}"
        )
        return CreateWorkflowResponse(
            success=response.success, message=response.message, workflow_id=workflow_dict["id"]
        )

    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in create_workflow: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.get("/{workflow_id}", response_model=WorkflowResponse)
async def get_workflow(
    workflow_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Retrieve a workflow by its ID.

    This endpoint fetches a workflow from the workflow service and returns its details.
    The workflow data is returned with any JSON string fields parsed into proper objects.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to retrieve

    ## Response
    Returns the workflow details if found.

    ## Errors
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        response = await workflow_service.get_workflow(
            workflow_id=workflow_id,
            user_id=user_id,
        )

        handle_grpc_response_status(response)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(response.workflow, preserving_proto_field_name=True)
        )

        workflow_dict = parse_json_tags(workflow_dict)

        return WorkflowResponse(
            success=response.success, message=response.message, workflow=workflow_dict
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.get(
    "/orchestration/{workflow_id}",
    dependencies=[Depends(validate_server_auth_key)],
)
async def get_workflow_orchestration(workflow_id: str):
    """
    Retrieve workflow details for orchestration.

    This endpoint fetches workflow details based on the provided `workflow_id`.
    It ensures that only authorized services with a valid server authentication key can access the workflow data.

    :param workflow_id: The unique identifier of the workflow.
    :type workflow_id: int
    :return: A response containing workflow details if found.
    :rtype: WorkflowResponse
    :raises HTTPException 404: If the workflow is not found.
    :raises HTTPException 500: If an internal server error occurs.
    """
    try:
        print(f"Workflow Id: {workflow_id}")

        response = await workflow_service.get_workflow(workflow_id=workflow_id)

        print(f"response: {response}")

        if not response.success:
            raise HTTPException(status_code=404, detail=response.message)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(response.workflow, preserving_proto_field_name=True)
        )

        print(f"Workflow_Dict: {workflow_dict}")

        # Parse `tags fields if they are JSON strings
        if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
            try:
                workflow_dict["tags"] = json.loads(workflow_dict["tags"])
            except json.JSONDecodeError:
                workflow_dict["tags"] = None

        return WorkflowResponse(
            success=response.success, message=response.message, workflow=workflow_dict
        )

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.delete("/{workflow_id}", response_model=DeleteWorkflowResponse)
async def delete_workflow(
    workflow_id: str, current_user: dict = Depends(role_required(["user", "admin"]))
):
    """
    Delete a workflow by its ID.

    This endpoint deletes a workflow from the system. Only the workflow owner or an admin
    can delete a workflow.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to delete

    ## Response
    Returns a success message if the deletion was successful.

    ## Errors
    - 400: Bad request (validation failed)
    - 403: Permission denied (not authorized to delete this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        # Validate user
        user_details = await validate_user(current_user["user_id"])

        # Delete the workflow
        response = await workflow_service.delete_workflow(
            workflow_id=workflow_id, owner_details=user_details
        )

        handle_grpc_response_status(response)
        return DeleteWorkflowResponse(success=response.success, message=response.message)

    except Exception as e:
        print(f"[EXCEPTION] {e}")
        raise HTTPException(status_code=500, detail=str(e))


@workflow_router.get("", response_model=PaginatedWorkflowResponse)
async def list_workflows(
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of workflows per page (1-100)"),
    category: Optional[str] = Query(None, description="Filter by workflow category"),
    status: Optional[str] = Query(
        None, description="Filter by workflow status (active/inactive/draft)"
    ),
    visibility: Optional[str] = Query(
        None, description="Filter by workflow visibility (public/private)"
    ),
    search: Optional[str] = Query(
        None, description="Search term to filter workflows by name or description"
    ),
    tags: Optional[str] = Query(
        None,
        description="Filter by tags using comma-separated key:value pairs (e.g., 'department:sales,priority:high')",
    ),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Retrieve a paginated list of all workflows.

    This endpoint returns a list of workflows with pagination metadata.
    Users with "user" or "admin" roles can access this endpoint.

    ## Filters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of workflows per page (1-100)
    - **category**: Filter by workflow category
    - **status**: Filter by workflow status (active/inactive/draft)
    - **visibility**: Filter by workflow visibility (public/private)
    - **search**: Search term to filter workflows by name or description
    - **tags**: Filter by tags using comma-separated key:value pairs

    ## Tag Format
    Tags should be provided as comma-separated key:value pairs, for example:
    `department:sales,priority:high,version:1.0`

    ## Response
    Returns a paginated list of workflows matching the filter criteria.

    ## Example
    ```
    GET /workflows?page=1&page_size=10&category=automation&status=active&search=customer&tags=priority:high
    ```
    """
    try:
        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        # Validate enum values if provided
        if category and category not in [c.value for c in WorkflowCategoryEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid category: {category}")

        # Update to include "draft" status
        valid_statuses = [s.value for s in WorkflowStatusEnum] + ["draft"]
        if status and status not in valid_statuses:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid status: {status}. Valid values are: {', '.join(valid_statuses)}",
            )

        if visibility and visibility not in [v.value for v in WorkflowVisibilityEnum]:
            raise HTTPException(status_code=400, detail=f"Invalid visibility: {visibility}")

        # Parse tags if provided (format: "key1:value1,key2:value2")
        parsed_tags = None
        if tags:
            try:
                parsed_tags = {}
                for tag_pair in tags.split(","):
                    if ":" in tag_pair:
                        key, value = tag_pair.split(":", 1)
                        parsed_tags[key.strip()] = value.strip()
            except Exception as e:
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid tags format. Use 'key1:value1,key2:value2': {str(e)}",
                )

        response = await workflow_service.list_workflows(
            page=page,
            page_size=page_size,
            category=category,
            status=status,
            visibility=visibility,
            search=search,
            tags=parsed_tags,
            user_id=user_id,
        )

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Convert JSON string fields to dictionaries as pydantic model requires
            if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
                try:
                    workflow_dict["tags"] = json.loads(workflow_dict["tags"])
                except json.JSONDecodeError:
                    workflow_dict["tags"] = None

            workflows.append(WorkflowInDB.model_validate(workflow_dict))

        # Calculate pagination metadata
        total = response.total
        total_pages = response.total_pages
        current_page = page
        has_next_page = current_page < total_pages
        has_previous_page = current_page > 1

        metadata = PaginationMetadata(
            total=total,
            totalPages=total_pages,
            currentPage=current_page,
            pageSize=page_size,
            hasNextPage=has_next_page,
            hasPreviousPage=has_previous_page,
        )

        return PaginatedWorkflowResponse(data=workflows, metadata=metadata)
    except Exception as e:
        print(f"[ERROR] Unexpected error in list_workflows: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


# Version Management Endpoints


@workflow_router.get("/{workflow_id}/versions", response_model=ListWorkflowVersionsResponse)
async def list_workflow_versions(
    workflow_id: str,
    page: int = Query(1, ge=1, description="Page number for pagination (starts at 1)"),
    page_size: int = Query(10, ge=1, le=100, description="Number of versions per page (1-100)"),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    List all versions of a workflow.

    This endpoint returns a paginated list of all versions for a specific workflow.
    Both workflow owners and marketplace users can access this endpoint, but:
    - Owners can see all versions of their workflows
    - Marketplace users can only see versions of public workflows

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow

    ## Query Parameters
    - **page**: Page number for pagination (starts at 1)
    - **page_size**: Number of versions per page (1-100)

    ## Response
    Returns a paginated list of workflow versions with metadata.

    ## Errors
    - 403: Permission denied (private workflow accessed by non-owner)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        response = await workflow_service.list_workflow_versions(
            workflow_id=workflow_id, user_id=user_id, page=page, page_size=page_size
        )

        handle_grpc_response_status(response)

        # Convert protobuf versions to dict format
        versions = []
        for version in response.versions:
            version_dict = prepare_workflow_dict(
                MessageToDict(version, preserving_proto_field_name=True)
            )
            versions.append(WorkflowVersionInDB.model_validate(version_dict))
        print(f"[DEBUG] Workflow versions: {versions}")
        return ListWorkflowVersionsResponse(
            success=response.success,
            message=response.message,
            versions=versions,
            total=response.total,
            page=response.page,
            total_pages=response.total_pages,
            current_version_id=response.current_version_id,
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in list_workflow_versions: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.get(
    "/{workflow_id}/versions/{version_id}", response_model=GetWorkflowVersionResponse
)
async def get_workflow_version(
    workflow_id: str,
    version_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Get details of a specific workflow version.

    This endpoint returns detailed information about a specific version of a workflow.
    Both workflow owners and marketplace users can access this endpoint, but:
    - Owners can see all versions of their workflows
    - Marketplace users can only see versions of public workflows

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow
    - **version_id**: The unique identifier of the version

    ## Response
    Returns detailed information about the specified workflow version.

    ## Errors
    - 403: Permission denied (private workflow accessed by non-owner)
    - 404: Workflow or version not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        response = await workflow_service.get_workflow_version(
            workflow_id=workflow_id, version_id=version_id, user_id=user_id
        )

        handle_grpc_response_status(response)

        # Convert protobuf version to dict format
        version_dict = prepare_workflow_dict(
            MessageToDict(response.version, preserving_proto_field_name=True)
        )
        version = WorkflowVersionInDB.model_validate(version_dict)

        return GetWorkflowVersionResponse(
            success=response.success, message=response.message, version=version
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in get_workflow_version: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post(
    "/{workflow_id}/versions/{version_id}/switch", response_model=SwitchWorkflowVersionResponse
)
async def switch_workflow_version(
    workflow_id: str,
    version_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Switch the current version of a workflow.

    This endpoint allows workflow owners to switch the current active version of their workflow.
    Only the workflow owner can perform this operation.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow
    - **version_id**: The unique identifier of the version to switch to

    ## Response
    Returns success status and details of the new current version.

    ## Errors
    - 403: Permission denied (only workflow owner can switch versions)
    - 404: Workflow or version not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        response = await workflow_service.switch_workflow_version(
            workflow_id=workflow_id, version_id=version_id, user_id=user_id
        )

        handle_grpc_response_status(response)

        # Convert protobuf version to dict format
        new_current_version_dict = prepare_workflow_dict(
            MessageToDict(response.new_current_version, preserving_proto_field_name=True)
        )
        new_current_version = WorkflowVersionInDB.model_validate(new_current_version_dict)

        return SwitchWorkflowVersionResponse(
            success=response.success,
            message=response.message,
            new_current_version=new_current_version,
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in switch_workflow_version: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post("/{workflow_id}/pull-updates", response_model=UpdateWorkflowResponse)
async def pull_updates_from_source(
    workflow_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Pull updates from the source workflow for a cloned workflow.

    This endpoint allows users to update their cloned workflow with the latest
    changes from the source workflow. After pulling updates, the is_changes_marketplace
    flag will be reset to false.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the cloned workflow to update

    ## Response
    Returns success status and details about the update operation.

    ## Errors
    - 400: Bad request (workflow is not cloned, no source workflow found)
    - 403: Permission denied (not the owner of the workflow)
    - 404: Workflow or source workflow not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # Use the new gRPC method to pull updates
        grpc_response = await workflow_service.pull_updates_from_source(
            workflow_id=workflow_id, user_id=user_id
        )

        handle_grpc_response_status(grpc_response)

        return UpdateWorkflowResponse(success=grpc_response.success, message=grpc_response.message)

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in pull_updates_from_source: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.get("/{workflow_id}/check-updates")
async def check_for_updates(
    workflow_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Check if a cloned workflow has updates available from its source workflow.

    This endpoint checks if the source workflow has been updated since this workflow
    was last synced. Returns the is_changes_marketplace flag and additional metadata.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the cloned workflow to check

    ## Response
    Returns information about available updates.

    ## Errors
    - 400: Bad request (workflow is not cloned)
    - 403: Permission denied (not the owner of the workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # Use the new gRPC method to check for updates
        grpc_response = await workflow_service.check_for_updates(
            workflow_id=workflow_id, user_id=user_id
        )

        handle_grpc_response_status(grpc_response)

        return {
            "success": grpc_response.success,
            "has_updates": grpc_response.has_updates,
            "source_workflow_id": grpc_response.source_workflow_id,
            "last_updated": grpc_response.last_updated,
            "source_last_updated": grpc_response.source_last_updated,
            "message": grpc_response.message,
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in check_for_updates: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post("/{workflow_id}/create-version-and-publish")
async def create_version_and_publish(
    workflow_id: str,
    publish_to_marketplace: bool = Query(
        False, description="Whether to update marketplace listing with new version"
    ),
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Create a new version for a workflow and optionally publish to marketplace.

    This is the comprehensive solution that replaces sync_workflow_to_marketplace.
    It provides user-controlled versioning where users explicitly decide when to create
    versions and whether to publish to marketplace.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to create version for

    ## Query Parameters
    - **publish_to_marketplace**: Whether to update marketplace listing with new version (default: false)

    ## Response
    Returns version creation and marketplace publishing status.

    ## Requirements
    - User must be the owner of the workflow
    - Workflow must have pending changes (is_updated = true)
    - For marketplace publishing: workflow must be public and have existing marketplace listing

    ## Errors
    - 400: Bad request (no pending changes, workflow not public for marketplace publishing)
    - 403: Permission denied (not the owner of the workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # Call the gRPC service to create version and optionally publish
        grpc_response = await workflow_service.create_version_and_publish(
            workflow_id=workflow_id, user_id=user_id, publish_to_marketplace=publish_to_marketplace
        )

        handle_grpc_response_status(grpc_response)

        return {
            "success": grpc_response.success,
            "message": grpc_response.message,
            "version_created": grpc_response.version_created,
            "marketplace_updated": grpc_response.marketplace_updated,
            "version_number": (
                grpc_response.version_number if hasattr(grpc_response, "version_number") else None
            ),
            "version_id": (
                grpc_response.version_id if hasattr(grpc_response, "version_id") else None
            ),
            "marketplace_listing_id": (
                grpc_response.marketplace_listing_id
                if hasattr(grpc_response, "marketplace_listing_id")
                else None
            ),
        }

    except HTTPException:
        raise
    except Exception as e:
        print(f"[ERROR] Unexpected error in create_version_and_publish: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post(
    "/{workflow_id}/versions/{version_id}/clone", response_model=CreateWorkflowResponse
)
async def clone_workflow_from_version(
    workflow_id: str,
    version_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Clone a workflow from a specific version.

    This endpoint allows users to create a new workflow based on a specific version
    of an existing workflow. This is useful for:
    - Creating a personal copy of a public workflow
    - Branching from a specific version
    - Creating variations of existing workflows

    ## Path Parameters
    - **workflow_id**: The unique identifier of the source workflow
    - **version_id**: The unique identifier of the version to clone from

    ## Response
    Returns success status and the ID of the newly created workflow.

    ## Errors
    - 403: Permission denied (cannot access private workflow)
    - 404: Workflow or version not found
    - 500: Server error
    """
    try:
        user_id = current_user.get("user_id")

        # First get the version details
        version_response = await workflow_service.get_workflow_version(
            workflow_id=workflow_id, version_id=version_id, user_id=user_id
        )

        handle_grpc_response_status(version_response)

        # Create a new workflow from the version data
        version = version_response.version

        # Create new workflow with version data
        create_response = await workflow_service.create_workflow(
            name=f"{version.name} (Cloned)",
            workflow_data=json.loads(version.workflow_url) if version.workflow_url else {},
            start_nodes=(
                [json.loads(node) for node in version.start_nodes] if version.start_nodes else []
            ),
            owner_type="user",
            owner_details={"id": user_id},
            description=f"Cloned from version {version.version_number}: {version.description}",
            visibility="private",  # Always create as private
            category=version.category,
            tags=version.tags if version.tags else [],
            status="active",
        )

        handle_grpc_response_status(create_response)

        workflow_dict = prepare_workflow_dict(
            MessageToDict(create_response.workflow, preserving_proto_field_name=True)
        )
        return CreateWorkflowResponse(
            success=create_response.success,
            message=f"Successfully cloned workflow from version {version.version_number}",
            workflow_id=workflow_dict["id"],
        )

    except Exception as e:
        print(f"[ERROR] Unexpected error in clone_workflow_from_version: {str(e)}")
        error_details = parse_error(str(e))
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post("/by-ids", response_model=WorkflowsByIdsApiResponse)
async def get_workflows_by_ids(
    request: WorkflowsByIdsRequestPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Retrieves multiple workflows by their IDs.

    This endpoint allows fetching multiple workflows in a single request by providing a list of workflow IDs.

    ## Request Body
    - **ids**: List of workflow IDs to retrieve

    ## Response
    Returns a list of workflows matching the provided IDs and the total count.

    ## Example
    ```
    POST /workflows/by-ids
    {
        "ids": ["workflow-id-1", "workflow-id-2", "workflow-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No workflow IDs provided")

        print(f"[DEBUG] Fetching workflows by IDs: {request.ids}")
        response = await workflow_service.get_workflows_by_ids(workflow_ids=request.ids)

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Parse JSON string tags to dict if it's a string
            if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
                try:
                    workflow_dict["tags"] = json.loads(workflow_dict["tags"])
                except json.JSONDecodeError:
                    workflow_dict["tags"] = {}

            workflows.append(WorkflowInDB.model_validate(workflow_dict))

        return WorkflowsByIdsApiResponse(
            success=True,
            message=f"Retrieved {len(workflows)} workflows",
            workflows=workflows,
            total=len(workflows),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_workflows_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post(
    "/agent-platform/by-ids",
    response_model=WorkflowsByIdsApiResponse,
    dependencies=[Depends(validate_agent_platform_auth_key)],
)
async def get_workflows_by_ids_for_agent_platform_(request: WorkflowsByIdsRequestPayload):
    """
    Retrieves multiple workflows by their IDs for for_agent_platform.

    This endpoint allows fetching multiple workflows in a single request by providing a list of workflow IDs.

    ## Request Body
    - **ids**: List of workflow IDs to retrieve

    ## Response
    Returns a list of workflows matching the provided IDs and the total count.

    ## Example
    ```
    POST /workflows/by-ids
    {
        "ids": ["workflow-id-1", "workflow-id-2", "workflow-id-3"]
    }
    ```

    ## Errors
    - 400: Bad request (invalid input)
    - 500: Server error
    """
    try:
        if not request.ids:
            raise HTTPException(status_code=400, detail="No workflow IDs provided")

        print(f"[DEBUG] Fetching workflows by IDs: {request.ids}")
        response = await workflow_service.get_workflows_by_ids(workflow_ids=request.ids)

        workflows = []
        for workflow in response.workflows:
            workflow_dict = prepare_workflow_dict(
                MessageToDict(workflow, preserving_proto_field_name=True)
            )

            # Parse JSON string tags to dict if it's a string
            if "tags" in workflow_dict and isinstance(workflow_dict["tags"], str):
                try:
                    workflow_dict["tags"] = json.loads(workflow_dict["tags"])
                except json.JSONDecodeError:
                    workflow_dict["tags"] = {}

            workflows.append(WorkflowInDB.model_validate(workflow_dict))

        return WorkflowsByIdsApiResponse(
            success=True,
            message=f"Retrieved {len(workflows)} workflows",
            workflows=workflows,
            total=len(workflows),
        )
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[DEBUG] Error in get_workflows_by_ids: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.post(
    "/{workflow_id}/toggle-visibility", response_model=ToggleVisibilityResponseAPI
)
async def toggle_workflow_visibility_api(
    workflow_id: str,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Toggle the visibility of a workflow between PUBLIC and PRIVATE with marketplace integration.

    DEPRECATED: Please use the /update-details/{workflow_id} endpoint with toggle_visibility=true instead.

    This endpoint allows users to switch a workflow's visibility setting with a single API call.
    If the workflow is currently PUBLIC, it will be set to PRIVATE and removed from marketplace.
    If the workflow is currently PRIVATE, it will be set to PUBLIC and a marketplace listing will be created.

    ## Path Parameters
    - **workflow_id**: The unique identifier of the workflow to toggle

    ## Response
    Returns the updated workflow with its new visibility setting if successful.

    ## Errors
    - 400: Bad request (validation failed)
    - 403: Permission denied (not authorized to modify this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:
        # Validate user
        user_details = await validate_user(current_user["user_id"])

        # user_details = {
        #     "id": "91a237fd-0225-4e02-9e9f-805eff073b07"
        # }

        # Toggle workflow visibility
        grpc_response = await workflow_service.toggle_workflow_visibility(
            workflow_id=workflow_id, owner_details=user_details
        )

        handle_grpc_response_status(grpc_response)

        # Parse workflow data
        workflow_dict = MessageToDict(grpc_response.workflow, preserving_proto_field_name=True)
        workflow_dict = parse_json_tags(workflow_dict)
        workflow_dict = prepare_workflow_dict(workflow_dict)

        return ToggleVisibilityResponseAPI(
            success=grpc_response.success,
            message=grpc_response.message,
            workflow=WorkflowInDB.model_validate(workflow_dict),
        )
    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error_details = parse_error(str(e))
        print(f"[ERROR] Unexpected error in toggle_workflow_visibility_api: {str(e)}")
        raise HTTPException(status_code=error_details["code"], detail=error_details["message"])


@workflow_router.patch("/{workflow_id}", response_model=UpdateWorkflowResponse)
async def update_workflow_api(
    workflow_id: str,
    payload: WorkflowPatchPayload,
    current_user: dict = Depends(role_required(["user", "admin"])),
):
    """
    Update an existing workflow.

    DEPRECATED: Please use the /update-details/{workflow_id} endpoint instead.

    This endpoint allows users to update various aspects of a workflow including name,
    description, workflow data, start nodes, visibility, category, tags, and status.
    All fields are optional - only the provided fields will be updated.

    ## Request Body
    - **name**: Optional new name for the workflow
    - **description**: Optional new description
    - **workflow_data**: Optional updated workflow definition data
    - **start_node_data**: Optional updated start node data
    - **user_id**: Optional list of user ID with access to the workflow
    - **visibility**: Optional visibility setting (public/private)
    - **category**: Optional workflow category
    - **tags**: Optional key-value pairs for tagging the workflow
    - **status**: Optional workflow status
    - **version**: Optional version string
    - **is_changes_marketplace**: Optional marketplace flag

    ## Response
    Returns a success message if the update was successful.

    ## Errors
    - 400: Bad request (invalid input, validation failed)
    - 403: Permission denied (not authorized to update this workflow)
    - 404: Workflow not found
    - 500: Server error
    """
    try:

        user_id = None if current_user.get("role") == "admin" else current_user.get("user_id")

        # user_id = "91a237fd-0225-4e02-9e9f-805eff073b07"

        # Validate request data
        update_data = payload.model_dump(exclude_unset=True)
        if not update_data:
            raise HTTPException(status_code=400, detail="No fields provided for update.")

        # Update workflow
        grpc_response = await workflow_service.patch_workflow(
            workflow_id=workflow_id,
            update_fields=update_data,
            owner_details={"id": user_id},
        )

        handle_grpc_response_status(grpc_response)
        return UpdateWorkflowResponse(success=grpc_response.success, message=grpc_response.message)
    except HTTPException as http_exc:
        raise http_exc
    except ValueError as ve:
        print(f"[API ROUTE EXCEPTION] ValueError: {ve}")
        raise HTTPException(status_code=422, detail=str(ve))
    except Exception as e:
        print(f"[API ROUTE EXCEPTION] Unexpected error: {type(e).__name__} - {e}")
        raise HTTPException(status_code=500, detail=f"An internal server error occurred: {str(e)}")
