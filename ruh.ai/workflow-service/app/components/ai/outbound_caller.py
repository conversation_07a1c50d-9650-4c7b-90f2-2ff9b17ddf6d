from typing import ClassVar, List

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import InputBase, Output
from app.utils.workflow_builder.input_helpers import create_dual_purpose_input

# Note: The actual execution logic (the Pydantic models and the 'process' method)
# lives in your separate node-executor-service. This file is purely for defining
# the node's appearance and inputs/outputs for the workflow builder UI.

class StartOutboundCallComponent(BaseNode):
    """
    A user-facing definition for a node that initiates an outbound, AI-powered phone call.
    """

    # --- Static Component Definition for the UI ---
    name: ClassVar[str] = "StartOutboundCallComponent"
    display_name: ClassVar[str] = "Start AI Call"
    description: ClassVar[str] = "Places an outbound, AI-driven call to a specified phone number."
    
    # Based on our discussion, this node initiates an AI agent, so it belongs in the 'AI' category.
    category: ClassVar[str] = "AI"
    icon: ClassVar[str] = "PhoneCall" # A fitting icon name from a library like Lucide Icons

    # --- Input Definitions ---
    # We use `create_dual_purpose_input` for all fields to allow users to either
    # hard-code values or connect them dynamically from other nodes.
    inputs: ClassVar[List[InputBase]] = [
        create_dual_purpose_input(
            name="phone_no",
            display_name="Phone Number",
            input_type="string",
            required=True,
            info="The target phone number to call. Can be connected or entered directly.",
            input_types=["string", "number", "Any"],
        ),
        create_dual_purpose_input(
            name="systemprompt",
            display_name="System Prompt / Objective",
            input_type="text:long", # UI hint for a larger textarea
            required=False,
            info="The initial instructions, goal, or personality for the AI agent on the call.",
            input_types=["string", "Any"],
        ),
        create_dual_purpose_input(
            name="context",
            display_name="Context",
            input_type="code:json", # UI hint for a JSON editor
            required=False,
            value="{}", # Default to an empty JSON object
            info="A dictionary of key-value pairs (e.g., customer data, order details) to provide to the AI agent.",
            input_types=["dict", "string", "Any"],
        ),
        create_dual_purpose_input(
            name="user_id",
            display_name="User ID",
            input_type="string",
            required=False,
            info="The ID of the user or system initiating the call (optional).",
            input_types=["string", "Any"],
            advanced=True, # Mark as an advanced field, can be hidden by default in the UI
        ),
        create_dual_purpose_input(
            name="call_id",
            display_name="Call ID",
            input_type="string",
            required=False,
            info="A unique ID to associate with this call for tracking purposes (optional).",
            input_types=["string", "Any"],
            advanced=True,
        ),
        create_dual_purpose_input(
            name="agent_id",
            display_name="Agent ID",
            input_type="string",
            required=False,
            info="The specific ID of the pre-configured AI agent to use for the call (optional).",
            input_types=["string", "Any"],
            advanced=True,
        ),
        create_dual_purpose_input(
            name="transfer_no",
            display_name="Transfer Number",
            input_type="string",
            required=False,
            info="A phone number to transfer the call to if a human is requested (optional).",
            input_types=["string", "Any"],
            advanced=True,
        ),
    ]

    # --- Output Definitions ---
    # These outputs allow downstream nodes to use the results of the call initiation.
    outputs: ClassVar[List[Output]] = [
        Output(name="api_response", display_name="API Response", output_type="dict"),
        Output(name="status_code", display_name="Status Code", output_type="integer"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    # The `execute` method for the frontend-facing component is not used for actual
    # execution if you have a separate executor service. It can be a simple pass-through
    # or raise a NotImplementedError to prevent accidental local execution.
    async def execute(self, context):
        """
        This method is a placeholder. The actual execution is handled by the
        node-executor-service based on the component's 'name'.
        """
        raise NotImplementedError(
            f"Execution for '{self.name}' is handled by the dedicated executor service, not within this definition file."
        )