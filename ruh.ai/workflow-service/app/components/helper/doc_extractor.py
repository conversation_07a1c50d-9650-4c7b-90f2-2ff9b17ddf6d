from typing import Dict, Any, List, ClassVar
import os
import pathlib
import asyncio
from importlib.util import find_spec

from app.components.core.base_node import BaseNode
from app.models.workflow_builder.components import (
    InputBase,
    HandleInput,
    StringInput,
    DropdownInput,
)
from app.models.workflow_builder.components import Output


class DocExtractorComponent(BaseNode):
    """
    Extracts text content from document files.

    This component can extract text from various document formats including:
    - PDF files (requires pypdf package)
    - DOCX files (requires python-docx package)
    - TXT files (no additional dependencies)

    Dependencies:
    - For PDF support: pip install pypdf
    - For DOCX support: pip install python-docx
    
    NOTE: This component is currently disabled from the frontend.
    To re-enable, set is_abstract = False.
    """

    name: ClassVar[str] = "DocExtractorComponent"
    display_name: ClassVar[str] = "Document Extractor"
    description: ClassVar[str] = "Extracts text content from document files (PDF, DOCX, TXT)."

    category: ClassVar[str] = "Helpers"
    icon: ClassVar[str] = "FileText"
    beta: ClassVar[bool] = False
    is_abstract: ClassVar[bool] = True  # Disabled from frontend - set to False to re-enable

    inputs: ClassVar[List[InputBase]] = [
        # File path - connection handle
        HandleInput(
            name="file_path_handle",
            display_name="File Path",
            is_handle=True,
            input_types=["string"],
            info="Connect a file path from another node.",
        ),
        # File path - direct input in inspector
        StringInput(
            name="file_path",
            display_name="File Path (Direct)",
            required=False,
            is_handle=False,
            value="",
            info="Path to the document file. Used if no connection is provided.",
        ),
        # File type - dropdown
        DropdownInput(
            name="file_type",
            display_name="File Type",
            options=["Auto-Detect", "PDF", "DOCX", "TXT"],
            value="Auto-Detect",
            info="The type of document file to extract from.",
        ),
    ]

    outputs: ClassVar[List[Output]] = [
        Output(name="text_content", display_name="Text Content", output_type="string"),
        Output(name="page_count", display_name="Page Count", output_type="int"),
        Output(name="error", display_name="Error", output_type="string"),
    ]

    def build(self, **kwargs) -> Dict[str, Any]:
        """
        Legacy executor for the DocExtractorComponent.
        Extracts text content from a document file.

        Args:
            file_path_handle: Connected file path
            file_path: Direct input file path
            file_type: The type of document file ("Auto-Detect", "PDF", "DOCX", or "TXT")

        Returns:
            A dictionary with the extracted text content, page count, and any error message.
        """
        print(f"Executing {self.name} using legacy build method...")
        # Get input values
        file_path = kwargs.get("file_path_handle") or kwargs.get("file_path", "")
        file_type = kwargs.get("file_type", "Auto-Detect")

        # Initialize result
        result = {"text_content": "", "page_count": 0, "error": None}

        # Validate file path
        if not file_path:
            result["error"] = "No file path provided"
            return result

        if not os.path.exists(file_path):
            result["error"] = f"File not found: {file_path}"
            return result

        try:
            # Auto-detect file type if needed
            if file_type == "Auto-Detect":
                suffix = pathlib.Path(file_path).suffix.lower()
                if suffix == ".pdf":
                    file_type = "PDF"
                elif suffix == ".docx":
                    file_type = "DOCX"
                elif suffix == ".txt":
                    file_type = "TXT"
                else:
                    result["error"] = f"Unsupported file type: {suffix}"
                    return result

            # Extract text based on file type
            if file_type == "PDF":
                # Check if pypdf is installed
                if not find_spec("pypdf"):
                    result["error"] = (
                        "pypdf package is required for PDF extraction. Install with: pip install pypdf"
                    )
                    return result

                # Import pypdf here to avoid errors if not installed
                import pypdf

                try:
                    reader = pypdf.PdfReader(file_path)
                    text_parts = []

                    for page in reader.pages:
                        text_parts.append(page.extract_text())

                    result["text_content"] = "\n".join(text_parts)
                    result["page_count"] = len(reader.pages)
                except Exception as e:
                    result["error"] = f"Error extracting PDF: {str(e)}"

            elif file_type == "DOCX":
                # Check if python-docx is installed
                if not find_spec("docx"):
                    result["error"] = (
                        "python-docx package is required for DOCX extraction. Install with: pip install python-docx"
                    )
                    return result

                # Import docx here to avoid errors if not installed
                import docx

                try:
                    doc = docx.Document(file_path)
                    text_parts = []

                    for para in doc.paragraphs:
                        if para.text:
                            text_parts.append(para.text)

                    result["text_content"] = "\n".join(text_parts)
                    # DOCX doesn't have a reliable page count
                    result["page_count"] = None
                except Exception as e:
                    result["error"] = f"Error extracting DOCX: {str(e)}"

            elif file_type == "TXT":
                try:
                    with open(file_path, "r", encoding="utf-8", errors="ignore") as f:
                        result["text_content"] = f.read()
                    # TXT files are considered a single page
                    result["page_count"] = 1
                except Exception as e:
                    result["error"] = f"Error extracting TXT: {str(e)}"

            else:
                result["error"] = f"Unsupported file type: {file_type}"

        except Exception as e:
            result["error"] = f"Unexpected error: {str(e)}"

        return result
