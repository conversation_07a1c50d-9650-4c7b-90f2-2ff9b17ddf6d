"""Processing components for workflow builder.

This package contains components that process and transform data within workflows,
such as combining text, merging data, and more.

NOTE: SaveToFileComponent is currently disabled from the frontend by setting
is_abstract = True in its class definition. To re-enable it, set
is_abstract = False in the SaveToFileComponent class.
"""

from app.components.processing.combine_text import CombineTextComponent
from app.components.processing.merge_data import MergeDataComponent
from app.components.processing.message_to_data import MessageToDataComponent
from app.components.processing.data_to_dataframe import DataToDataFrameComponent
from app.components.processing.alter_metadata import AlterMetadataComponent
from app.components.processing.select_data import SelectDataComponent
from app.components.processing.split_text import SplitTextComponent
from app.components.processing.delay_time import DelayComponent
# from app.components.processing.save_to_file import SaveToFileComponent  # Currently disabled (is_abstract = True)


__all__ = [
    "CombineTextComponent",
    "MergeDataComponent",
    "MessageToDataComponent",
    "DataToDataFrameComponent",
    "AlterMetadataComponent",
    "SelectDataComponent",
    "SplitTextComponent",
    "DelayComponent",

    # "SaveToFileComponent",  # Currently disabled (is_abstract = True)
]
