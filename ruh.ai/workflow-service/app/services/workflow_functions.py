# app/services/workflow_service.py
import grpc
import json
import structlog
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal, get_db
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.models.workflow_rating import WorkflowRating
from app.grpc_ import workflow_pb2, workflow_pb2_grpc
from app.services.workflow_builder.workflow_schema_converter import (
    convert_workflow_to_transition_schema,
    validate_workflow_template_variables,
)
from app.utils.constants.constants import WorkflowStatusEnum, WorkflowVisibilityEnum
from app.utils.json_validator import validate_transition_schema
from app.utils.file_upload import GCSUploadService
from app.utils.kafka.kafka_service import KafkaProducer
from app.utils.workflow_data_transformer import extract_mcp_and_component_nodes_exact_format
from app.helpers.workflow_to_protobuf import _workflow_to_protobuf
from app.helpers.helpers import (
    _create_marketplace_listing_from_workflow,
    _update_derived_workflows_change_status,
)

# Initialize structured logger
logger = structlog.get_logger()


class WorkflowFunctions(workflow_pb2_grpc.WorkflowServiceServicer):
    def __init__(self):
        """Initialize the WorkflowService with a KafkaProducer instance"""
        self.kafka_producer = KafkaProducer()

    def get_db(self) -> Session:
        db = SessionLocal()
        try:
            return db
        finally:
            db.close()

    def createWorkflow(
        self, request: workflow_pb2.CreateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.CreateWorkflowResponse:
        db = SessionLocal()
        logger.info("create_workflow_request", name=request.name)
        try:
            # Parse workflow_data from string to JSON
            print("[DEBUG] Attempting to parse workflow_data JSON")
            try:
                workflow_data = json.loads(request.workflow_data)
                print("[DEBUG] Successfully parsed workflow_data JSON")
            except json.JSONDecodeError as e:
                print(f"[DEBUG] JSON parsing failed: {str(e)}")
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Invalid JSON format in workflow_data")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message="Invalid JSON format in workflow_data"
                )

            try:
                available_nodes = extract_mcp_and_component_nodes_exact_format(workflow_data)
                print("[DEBUG] Available nodes:", available_nodes)
            except Exception as e:
                print(f"[DEBUG] Available nodes extraction failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Available nodes extraction failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Available nodes extraction failed: {str(e)}"
                )

            # Upload original workflow to GCS
            print("[DEBUG] Uploading workflow to GCS")
            try:
                file_upload = GCSUploadService()
                gcs_response = file_upload.upload_json_as_file(workflow_data, "workflow_builders")
                builder_url = gcs_response.get("publicUrl")
                print("[DEBUG] GCS upload successful, got builder_url")
                if not builder_url:
                    print("[DEBUG] Failed to get public URL from GCS response")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to obtain public URL from GCS for builder workflow")
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for builder workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                )

            # Validate template variables before conversion
            print("[DEBUG] Validating template variables")
            try:
                template_validation = validate_workflow_template_variables(workflow_data)
                if not template_validation["valid"]:
                    error_msg = f"Template variable validation failed: {'; '.join(template_validation['errors'])}"
                    print(f"[DEBUG] Template variable validation failed: {error_msg}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(error_msg)
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False, message=error_msg
                    )

                # Log warnings if any
                if template_validation["warnings"]:
                    warnings_msg = "; ".join(template_validation["warnings"])
                    print(f"[DEBUG] Template variable warnings: {warnings_msg}")
                    logger.warning(f"Template variable warnings for workflow '{request.name}': {warnings_msg}")

                print(f"[DEBUG] Template variable validation successful: {template_validation['variable_summary']['total_count']} variables found")
            except Exception as e:
                print(f"[DEBUG] Template variable validation error: {str(e)}")
                logger.error(f"Template variable validation error: {str(e)}")
                # Continue with conversion - validation errors shouldn't block workflow creation

            # Convert workflow to transition schema (includes template variable preprocessing)
            print("[DEBUG] Converting workflow to transition schema")
            try:
                converted_workflow = convert_workflow_to_transition_schema(workflow_data)
                print("[DEBUG] Workflow conversion successful")
            except Exception as e:
                print(f"[DEBUG] Workflow conversion failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Workflow schema conversion failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Workflow schema conversion failed: {str(e)}"
                )

            # Validate converted workflow against transition_schema.json
            print("[DEBUG] Validating against transition_schema.json")
            try:
                validate_transition_schema(
                    data_input=converted_workflow,
                    schema_path="app/utils/shared/json_schemas/transition_schema.json",
                )
                print("[DEBUG] Transition schema validation successful")
            except Exception as e:
                print(f"[DEBUG] Transition schema validation failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"Transition schema validation failed: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"Transition schema validation failed: {str(e)}"
                )

            # Upload converted workflow to GCS
            print("[DEBUG] Uploading converted workflow to GCS")
            try:
                gcs_response = file_upload.upload_json_as_file(converted_workflow, "workflows")
                workflow_url = gcs_response.get("publicUrl")
                print("[DEBUG] Converted workflow upload successful")
                if not workflow_url:
                    print("[DEBUG] Failed to get public URL for converted workflow")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(
                        "Failed to obtain public URL from GCS for converted workflow"
                    )
                    return workflow_pb2.CreateWorkflowResponse(
                        success=False,
                        message="Failed to obtain public URL from GCS for converted workflow",
                    )
            except Exception as e:
                print(f"[DEBUG] Converted workflow GCS upload failed: {str(e)}")
                context.set_code(grpc.StatusCode.INTERNAL)
                context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                return workflow_pb2.CreateWorkflowResponse(
                    success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                )

            # Create new workflow
            print("[DEBUG] Creating new workflow object")
            new_workflow = Workflow(
                # Primary Fields
                name=request.name,
                workflow_url=workflow_url,
                builder_url=builder_url,
                start_nodes=[json.loads(node_json) for node_json in request.start_nodes],
                available_nodes=available_nodes,
                # Access Control
                owner_id=request.owner.id,
                user_ids=[request.owner.id],
                owner_type=workflow_pb2.WorkflowOwnerType.Name(request.owner_type).lower(),
                # Template Reference
                workflow_template_id=None,  # Set to None for new workflows
                template_owner_id=None,  # Set to None for new workflows
                is_imported=False,  # Default to False for new workflows
                # Additional Fields - Remove version field as it's now handled by WorkflowVersion
                # Task 1: is_updated - False for new workflows (no pending changes)
                is_updated=False,
                # Task 2: is_customizable - True for new workflows created from scratch
                is_customizable=True,
                # Additional Fields
                tags=list(request.tags) if request.tags else [],
            )

            print("[DEBUG] Adding workflow to database")
            db.add(new_workflow)
            db.flush()  # Flush to get the workflow ID

            # Create v1 version automatically
            print("[DEBUG] Creating v1 version for the workflow")
            v1_version = WorkflowVersion(
                workflow_id=new_workflow.id,
                version_number="1.0.0",
                name=request.name,
                description=(
                    request.description
                    if hasattr(request, "description") and request.description
                    else None
                ),
                workflow_url=workflow_url,
                builder_url=builder_url,
                start_nodes=[json.loads(node_json) for node_json in request.start_nodes],
                available_nodes=available_nodes,
                category=(
                    workflow_pb2.WorkflowCategory.Name(request.category).lower()
                    if hasattr(request, "category") and request.category
                    else None
                ),
                tags=list(request.tags) if request.tags else [],
                changelog="Initial version",
            )

            db.add(v1_version)
            db.flush()  # Flush to get the version ID

            # Set the current_version_id in the workflow
            new_workflow.current_version_id = v1_version.id

            db.commit()
            db.refresh(new_workflow)
            db.refresh(v1_version)
            print(
                f"[DEBUG] Workflow and v1 version saved to database successfully. Version ID: {v1_version.id}"
            )

            # Send Kafka notification
            print("[DEBUG] Sending Kafka notification")
            print(f"[DEBUG] WORKFLOW CREATED {new_workflow.id}")

            print("[DEBUG] Workflow creation completed successfully")
            return workflow_pb2.CreateWorkflowResponse(
                success=True,
                message=f"Workflow '{request.name}' created successfully with v1.0.0 version",
                workflow=_workflow_to_protobuf(new_workflow, db),
            )

        except Exception as e:
            print(f"[DEBUG] Unexpected error in createWorkflow: {str(e)}")
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("workflow_creation_failed", error=str(e))
            return workflow_pb2.CreateWorkflowResponse(
                success=False, message=f"Workflow creation failed: {str(e)}"
            )
        finally:
            print("[DEBUG] Closing database connection")
            db.close()

    def getWorkflow(
        self, request: workflow_pb2.GetWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.WorkflowResponse:
        """
        Retrieve a workflow by its ID.

        Args:
            request (workflow_pb2.GetWorkflowRequest): The request containing the workflow ID and optional user_id.
            context (grpc.ServicerContext): The gRPC context for handling the request.

        Returns:
            workflow_pb2.WorkflowResponse: The response containing the workflow details if found.

        Raises:
            grpc.RpcError: If the workflow is not found or an unexpected error occurs.
        """
        db = self.get_db()
        logger.info(
            "get_workflow_request",
            workflow_id=request.id,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with base query
            query = db.query(Workflow).filter(Workflow.id == request.id)

            # If user_id is provided, add filter to match owner_id
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Execute query
            workflow = query.first()

            if workflow is None:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found")
                return workflow_pb2.WorkflowResponse(success=False, message="Workflow not found")

            logger.info("workflow_retrieved", workflow_id=workflow.id)
            workflow11 = _workflow_to_protobuf(workflow, db)
            print(f"[WORKFLOW TO PROTOBUF] {workflow11}")
            return workflow_pb2.WorkflowResponse(
                success=True,
                message=f"Workflow {workflow.name} retrieved successfully",
                workflow=_workflow_to_protobuf(workflow, db),
            )
        except Exception as e:
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=e)
            return workflow_pb2.WorkflowResponse(success=False, message="Internal Server Error")
        finally:
            db.close()

    def deleteWorkflow(
        self, request: workflow_pb2.DeleteWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.DeleteWorkflowResponse:
        """
        Soft-deletes a workflow if it exists and belongs to the requesting user.

        Args:
            request (workflow_pb2.DeleteWorkflowRequest): Contains workflow ID and user ID.
            context (grpc.ServicerContext): gRPC context.

        Returns:
            workflow_pb2.DeleteWorkflowResponse: Indicates success or failure.
        """
        db = self.get_db()
        logger.info("delete_workflow_request", workflow_id=request.id, user_id=request.owner.id)

        try:
            workflow = (
                db.query(Workflow)
                .filter(
                    Workflow.id == request.id,
                    Workflow.owner_id == request.owner.id,
                    Workflow.deleted_at.is_(None),
                )
                .first()
            )

            if workflow is None:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("Workflow not found or access denied")
                return workflow_pb2.DeleteWorkflowResponse(
                    success=False,
                    message="Workflow not found or you do not have permission to delete it",
                )

            workflow.deleted_at = datetime.utcnow()
            workflow.status = WorkflowStatusEnum.INACTIVE
            db.commit()

            # db.query(Workflow).filter(
            #     Workflow.workflow_template_id == workflow.id,
            #     Workflow.deleted_at.is_(None)
            # ).update(
            #     { "status": WorkflowStatusEnum.INACTIVE },
            #     synchronize_session=False
            # )
            # db.commit()

            logger.info(
                "Workflow Deleted Successfully & marked as inactive", workflow_id=workflow.id
            )

            return workflow_pb2.DeleteWorkflowResponse(
                success=True, message=f"Workflow {workflow.name} soft-deleted successfully"
            )

        except Exception as e:
            db.rollback()
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("internal_server_error", error=str(e))
            return workflow_pb2.DeleteWorkflowResponse(
                success=False, message="Internal server error"
            )

        finally:
            db.close()

    def listWorkflows(
        self, request: workflow_pb2.ListWorkflowsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ListWorkflowsResponse:
        """
        Retrieve a paginated list of workflows with optional filtering by user_id.

        Args:
            request (workflow_pb2.ListWorkflowsRequest): The request object containing pagination details and filters.
            context (grpc.ServicerContext): The gRPC context for handling errors and metadata.

        Returns:
            workflow_pb2.ListWorkflowsResponse: A response containing the list of workflows, total count, current page, and total pages.

        Raises:
            grpc.StatusCode.INTERNAL: If any error occurs during the database query.
        """
        db = self.get_db()
        page = request.page
        page_size = request.page_size
        logger.info(
            "list_workflows_request",
            page=page,
            page_size=page_size,
            user_id=request.user_id if request.HasField("user_id") else None,
        )
        try:
            # Start with a base query
            query = db.query(Workflow)

            # Apply user_id filter if provided
            if request.HasField("user_id") and request.user_id:
                logger.info("filtering_by_user_id", user_id=request.user_id)
                query = query.filter(Workflow.owner_id == request.user_id)

            # Apply filters if provided
            if request.category:
                category_value = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                logger.info("filtering_by_category", category=category_value)
                query = query.filter(Workflow.category == category_value)

            if request.status:
                status_value = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                logger.info("filtering_by_status", status=status_value)
                query = query.filter(Workflow.status == status_value)

            if request.visibility:
                visibility_value = workflow_pb2.WorkflowVisibility.Name(request.visibility).lower()
                logger.info("filtering_by_visibility", visibility=visibility_value)
                query = query.filter(Workflow.visibility == visibility_value)

            # Add search filter for name, description, and category
            if request.search:
                search_term = f"%{request.search}%"
                logger.info("filtering_by_search", search=request.search)
                query = query.filter(
                    db.or_(
                        Workflow.name.ilike(search_term),
                        Workflow.description.ilike(search_term),
                        Workflow.category.ilike(search_term),
                    )
                )

            # Add tags filter
            if request.tags:
                logger.info("filtering_by_tags", tags=request.tags)
                # Filter by tags (exact match for any tag in the list)
                query = query.filter(Workflow.tags.contains(request.tags))

            query = query.order_by(Workflow.created_at.desc())

            # Get total count with filters applied
            total = query.count()

            # Apply pagination
            workflows = query.offset((page - 1) * page_size).limit(page_size).all()

            total_pages = (total + page_size - 1) // page_size  # Calculate total pages

            workflow_list = [_workflow_to_protobuf(workflow, db) for workflow in workflows]

            print(f"[DEBUG] Workflow list: {workflow_list}")

            logger.info("workflows_retrieved", total=total, page=page, total_pages=total_pages)

            return workflow_pb2.ListWorkflowsResponse(
                workflows=workflow_list,
                total=total,
                page=page,
                total_pages=total_pages,
            )
        except Exception as e:
            print(f"[DEBUG] Unexpected error in listWorkflows: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(str(e))
            logger.error("Internal_server_error", error=e)
            return workflow_pb2.ListWorkflowsResponse()  # Return empty response on error
        finally:
            db.close()

    def getWorkflowsByIds(
        self, request: workflow_pb2.GetWorkflowsByIdsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.GetWorkflowsByIdsResponse:
        logger.info("get_workflows_by_ids_request", ids_count=len(request.ids))
        print(f"[DEBUG] gRPC Servicer: getWorkflowsByIds called with IDs: {request.ids}")

        if not request.ids:
            logger.warn("get_workflows_by_ids_request_empty_ids")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                workflows=[], total=0, success=True, message="No IDs provided to fetch."
            )

        try:
            db = self.get_db()
            # Query Workflows by the provided IDs
            workflow_models = db.query(Workflow).filter(Workflow.id.in_(request.ids)).all()

            total = len(workflow_models)

            # Convert to protobuf format
            # You'll need a helper similar to _mcp_to_protobuf for workflows
            workflow_list_proto = [_workflow_to_protobuf(wf, db) for wf in workflow_models]

            print(f"[DEBUG] Workflow list: {workflow_list_proto}")
            logger.info(
                "workflows_retrieved_by_ids",
                retrieved_count=len(workflow_list_proto),
                requested_count=len(request.ids),
            )

            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=True,
                message=f"Successfully retrieved workflows.",
                workflows=workflow_list_proto,
                total=total,
            )
        except Exception as e:
            print(f"[DEBUG] Error fetching workflows by IDs: {str(e)}")
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error while fetching workflows by IDs: {str(e)}")
            return workflow_pb2.GetWorkflowsByIdsResponse(
                success=False, message="Internal server error."
            )
        finally:
            db.close()

    def toggleWorkflowVisibility(
        self, request: workflow_pb2.ToggleWorkflowVisibilityRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.ToggleWorkflowVisibilityResponse:
        db = self.get_db()
        logger.info(
            "toggle_workflow_visibility_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.workflow_id} not found.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Permission denied. You are not the owner."
                )

            message = ""
            if workflow.visibility == WorkflowVisibilityEnum.PRIVATE:
                # --- Going from PRIVATE to PUBLIC ---

                # Constraint: Cannot publish if cloned from another's template and minimally altered
                if (
                    workflow.is_imported
                    and workflow.template_owner_id
                    and workflow.template_owner_id != workflow.owner_id
                ):
                    err_msg = (
                        "This workflow was created from a template owned by another party. "
                        "To share your version publicly, please ensure it is significantly customized. "
                        "Direct republishing of minimally-altered cloned templates is restricted."
                    )
                    context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                    context.set_details(err_msg)
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message=err_msg
                    )

                workflow.visibility = WorkflowVisibilityEnum.PUBLIC
                # Note: is_updated field is not affected by visibility changes

                # Create marketplace listing for the current version
                marketplace_listing = _create_marketplace_listing_from_workflow(db, workflow)
                if not marketplace_listing:
                    db.rollback()
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details("Failed to create marketplace listing.")
                    return workflow_pb2.ToggleWorkflowVisibilityResponse(
                        success=False, message="Failed to prepare workflow for public visibility."
                    )

                message = f"Workflow '{workflow.name}' is now PUBLIC and listed in the marketplace."

            elif workflow.visibility == WorkflowVisibilityEnum.PUBLIC:
                # --- Going from PUBLIC to PRIVATE ---
                workflow.visibility = WorkflowVisibilityEnum.PRIVATE
                # workflow.is_changes_marketplace = False # Optional: Reset this flag

                # Deactivate marketplace listings for this workflow
                marketplace_listings = (
                    db.query(WorkflowMarketplaceListing)
                    .filter(
                        WorkflowMarketplaceListing.workflow_id == workflow.id,
                        WorkflowMarketplaceListing.listed_by_user_id == workflow.owner_id,
                    )
                    .all()
                )

                for listing in marketplace_listings:
                    listing.status = WorkflowStatusEnum.INACTIVE
                    db.add(listing)
                    logger.info(
                        f"Deactivated marketplace listing {listing.id} as workflow {workflow.id} made private."
                    )

                message = f"Workflow '{workflow.name}' is now PRIVATE and removed from marketplace."
            else:
                # Should not happen if visibility is always one of the defined enums
                context.set_code(grpc.StatusCode.FAILED_PRECONDITION)
                context.set_details(
                    f"Workflow {workflow.id} has an unknown visibility state: {workflow.visibility}"
                )
                return workflow_pb2.ToggleWorkflowVisibilityResponse(
                    success=False, message="Workflow has an unknown visibility state."
                )

            db.commit()
            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)
            # if 'linked_template' in locals() and linked_template: db.refresh(linked_template)

            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=True,
                message=message,
                workflow=_workflow_to_protobuf(workflow),  # Ensure this reflects new state
            )

        except Exception as e:
            db.rollback()
            logger.error("toggle_workflow_visibility_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to toggle workflow visibility: {str(e)}")
            return workflow_pb2.ToggleWorkflowVisibilityResponse(
                success=False, message=f"Failed to toggle workflow visibility: {str(e)}"
            )
        finally:
            db.close()

    def updateWorkflow(
        self, request: workflow_pb2.UpdateWorkflowRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_request", workflow_id=request.id, update_mask=request.update_mask.paths
        )

        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.id).first()
            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details(f"Workflow with ID {request.id} not found")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message=f"Workflow with ID {request.id} not found"
                )

            # Ownership check (assuming request.owner is present and has an id)
            if not hasattr(request, "owner") or not request.owner.id:
                context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                context.set_details("Owner information missing in request.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Owner information missing."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowResponse(
                    success=False, message="Permission denied."
                )

            # Get current version to update it alongside main workflow
            current_version = None
            if workflow.current_version_id:
                current_version = (
                    db.query(WorkflowVersion)
                    .filter(WorkflowVersion.id == workflow.current_version_id)
                    .first()
                )

            file_upload_service = GCSUploadService()  # Initialize GCS service
            template_relevant_fields_changed = False
            version_relevant_fields_changed = False

            # Process workflow_data only if it's in the update_mask
            if "workflow_data" in request.update_mask.paths:
                print("[DEBUG] 'workflow_data' is in update_mask. Processing...")
                if not request.workflow_data:
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details(
                        "workflow_data field is in update_mask but no data provided."
                    )
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="workflow_data provided in mask but is empty."
                    )
                try:
                    parsed_workflow_data = json.loads(request.workflow_data)
                    available_nodes = extract_mcp_and_component_nodes_exact_format(
                        parsed_workflow_data
                    )
                    workflow.available_nodes = available_nodes
                    print("[DEBUG] Successfully parsed workflow_data JSON for PATCH")
                except json.JSONDecodeError as e:
                    print(f"[DEBUG] JSON parsing failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                    context.set_details("Invalid JSON format in workflow_data")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message="Invalid JSON format in workflow_data"
                    )

                # Upload original workflow to GCS
                try:
                    gcs_response_builder = file_upload_service.upload_json_as_file(
                        parsed_workflow_data, "workflow_builders"
                    )
                    builder_url = gcs_response_builder.get("publicUrl")

                    if not builder_url:
                        raise Exception("Failed to get public URL from GCS for builder workflow")
                    workflow.builder_url = builder_url  # Update the model field
                    print(f"[DEBUG] GCS builder upload successful for PATCH: {builder_url}")
                except Exception as e:
                    # (Error handling as before, but specific to this block)
                    print(f"[DEBUG] GCS builder upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for builder workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for builder workflow: {str(e)}"
                    )

                # Convert workflow to transition schema
                try:
                    print("[DEBUG] Starting workflow conversion for PATCH")
                    print(f"[DEBUG] Workflow data keys: {list(parsed_workflow_data.keys())}")
                    print(f"[DEBUG] Number of nodes: {len(parsed_workflow_data.get('nodes', []))}")
                    print(f"[DEBUG] Number of edges: {len(parsed_workflow_data.get('edges', []))}")

                    # Log node types and data
                    for i, node in enumerate(parsed_workflow_data.get("nodes", [])):
                        node_data = node.get("data", {})
                        print(
                            f"[DEBUG] Node {i}: id={node.get('id')}, type={node_data.get('type')}, originalType={node_data.get('originalType')}"
                        )

                    # Log edge data
                    for i, edge in enumerate(parsed_workflow_data.get("edges", [])):
                        print(
                            f"[DEBUG] Edge {i}: id={edge.get('id')}, source={edge.get('source')}, target={edge.get('target')}, sourceHandle={edge.get('sourceHandle')}"
                        )

                    # Validate template variables before conversion
                    print("[DEBUG] Validating template variables for PATCH")
                    try:
                        template_validation = validate_workflow_template_variables(parsed_workflow_data)
                        if not template_validation["valid"]:
                            error_msg = f"Template variable validation failed: {'; '.join(template_validation['errors'])}"
                            print(f"[DEBUG] Template variable validation failed for PATCH: {error_msg}")
                            context.set_code(grpc.StatusCode.INVALID_ARGUMENT)
                            context.set_details(error_msg)
                            return workflow_pb2.UpdateWorkflowResponse(
                                success=False, message=error_msg
                            )

                        # Log warnings if any
                        if template_validation["warnings"]:
                            warnings_msg = "; ".join(template_validation["warnings"])
                            print(f"[DEBUG] Template variable warnings for PATCH: {warnings_msg}")
                            logger.warning(f"Template variable warnings for workflow update '{workflow.name}': {warnings_msg}")

                        print(f"[DEBUG] Template variable validation successful for PATCH: {template_validation['variable_summary']['total_count']} variables found")
                    except Exception as e:
                        print(f"[DEBUG] Template variable validation error for PATCH: {str(e)}")
                        logger.error(f"Template variable validation error: {str(e)}")
                        # Continue with conversion - validation errors shouldn't block workflow updates

                    converted_workflow = convert_workflow_to_transition_schema(parsed_workflow_data)
                    print("[DEBUG] Workflow conversion successful for PATCH")
                except Exception as e:
                    print(f"[DEBUG] Workflow conversion failed for PATCH: {str(e)}")
                    print(f"[DEBUG] Exception type: {type(e).__name__}")

                    # Add detailed traceback for NoneType errors
                    if "NoneType" in str(e):
                        import traceback

                        print(f"[DEBUG] Full traceback for NoneType error:")
                        traceback.print_exc()

                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Workflow schema conversion failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Workflow schema conversion failed: {str(e)}"
                    )

                # Validate converted workflow
                try:
                    validate_transition_schema(
                        data_input=converted_workflow,
                        schema_path="app/utils/shared/json_schemas/transition_schema.json",
                    )
                    print("[DEBUG] Transition schema validation successful for PATCH")
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Transition schema validation failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"Transition schema validation failed: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"Transition schema validation failed: {str(e)}"
                    )

                # Upload converted workflow to GCS
                try:
                    gcs_response_workflow = file_upload_service.upload_json_as_file(
                        converted_workflow, "workflows"
                    )
                    workflow_url = gcs_response_workflow.get("publicUrl")
                    if not workflow_url:
                        raise Exception("Failed to get public URL from GCS for converted workflow")
                    workflow.workflow_url = workflow_url  # Update the model field
                    print(
                        f"[DEBUG] Converted workflow GCS upload successful for PATCH: {workflow_url}"
                    )
                except Exception as e:
                    # (Error handling as before)
                    print(f"[DEBUG] Converted GCS workflow upload failed for PATCH: {str(e)}")
                    context.set_code(grpc.StatusCode.INTERNAL)
                    context.set_details(f"GCS upload failed for converted workflow: {str(e)}")
                    return workflow_pb2.UpdateWorkflowResponse(
                        success=False, message=f"GCS upload failed for converted workflow: {str(e)}"
                    )

                # Also update current version if it exists
                if current_version:
                    current_version.workflow_url = workflow_url
                    current_version.builder_url = builder_url
                    # Extract available nodes for current version
                    try:
                        available_nodes = extract_mcp_and_component_nodes_exact_format(
                            parsed_workflow_data
                        )
                        current_version.available_nodes = available_nodes
                    except Exception as e:
                        logger.warning(
                            f"Failed to extract available nodes for current version: {str(e)}"
                        )
                    db.add(current_version)

                template_relevant_fields_changed = True
                version_relevant_fields_changed = True

            # Update other fields based on FieldMask
            for field_path in request.update_mask.paths:
                if field_path == "name":
                    workflow.name = request.name
                    # Also update current version if it exists
                    if current_version:
                        current_version.name = request.name
                        db.add(current_version)
                    version_relevant_fields_changed = True
                    template_relevant_fields_changed = (
                        True  # Name changes should trigger derived workflow updates
                    )
                elif field_path == "description":
                    workflow.description = request.description
                    # Also update current version if it exists
                    if current_version:
                        current_version.description = request.description
                        db.add(current_version)
                    version_relevant_fields_changed = True
                    template_relevant_fields_changed = (
                        True  # Description changes should trigger derived workflow updates
                    )
                elif field_path == "start_nodes":
                    workflow.start_nodes = [
                        json.loads(node_json) for node_json in request.start_nodes
                    ]
                    # Also update current version if it exists
                    if current_version:
                        current_version.start_nodes = [
                            json.loads(node_json) for node_json in request.start_nodes
                        ]
                        db.add(current_version)
                    version_relevant_fields_changed = True
                    template_relevant_fields_changed = (
                        True  # Start nodes changes should trigger derived workflow updates
                    )
                elif field_path == "user_ids":
                    workflow.user_ids = list(request.user_ids) if request.user_ids else []
                elif field_path == "visibility":
                    workflow.visibility = workflow_pb2.WorkflowVisibility.Name(
                        request.visibility
                    ).lower()
                elif field_path == "category":
                    workflow.category = workflow_pb2.WorkflowCategory.Name(request.category).lower()
                    # Also update current version if it exists
                    if current_version:
                        current_version.category = workflow_pb2.WorkflowCategory.Name(
                            request.category
                        ).lower()
                        db.add(current_version)
                elif field_path == "tags":  # tags is repeated string in proto, array in DB
                    workflow.tags = list(request.tags) if request.tags else []
                    # Also update current version if it exists
                    if current_version:
                        current_version.tags = list(request.tags) if request.tags else []
                        db.add(current_version)
                elif field_path == "status":
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                elif field_path == "is_updated" and hasattr(request, "is_updated"):
                    # Allow explicitly setting the is_updated flag via update
                    workflow.is_updated = request.is_updated
                    logger.info(
                        f"Explicitly set is_updated={request.is_updated} for workflow {workflow.id}"
                    )
                elif field_path == "is_customizable" and request.is_customizable:
                    # Allow updating is_customizable field
                    workflow.is_customizable = request.is_customizable

            # Update marketplace listing if this workflow is public and has marketplace changes enabled
            if (
                template_relevant_fields_changed
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
            ):
                logger.info(f"Updating marketplace listing for public workflow {workflow.id}.")
                # Update the marketplace listing with the new workflow version
                marketplace_listing = _create_marketplace_listing_from_workflow(db, workflow)
                if marketplace_listing:
                    logger.info(
                        f"Updated marketplace listing {marketplace_listing.id} for workflow {workflow.id}"
                    )
                else:
                    logger.warning(
                        f"Failed to update marketplace listing for workflow {workflow.id}"
                    )

            # Set is_updated=True if version-relevant fields were changed
            if version_relevant_fields_changed:
                print("[DEBUG] Version-relevant fields changed, setting is_updated=True")
                workflow.is_updated = True
                logger.info(
                    f"Set is_updated=True for workflow {workflow.id} due to version-relevant changes"
                )

            db.add(workflow)  # Add workflow to session
            db.commit()

            # Task 1: Update derived workflows if this workflow has marketplace changes enabled
            logger.info(
                f"Checking derived workflow update conditions: template_relevant_fields_changed={template_relevant_fields_changed}, "
                f"workflow.visibility={workflow.visibility}, is_public={workflow.visibility == WorkflowVisibilityEnum.PUBLIC}"
            )
            if (
                template_relevant_fields_changed
                and workflow.visibility == WorkflowVisibilityEnum.PUBLIC
            ):
                logger.info(f"Triggering derived workflow update for workflow {workflow.id}")
                _update_derived_workflows_change_status(db, workflow)
            else:
                logger.info(f"Skipping derived workflow update for workflow {workflow.id}")

            db.refresh(workflow)
            # if 'template' in locals() and template: db.refresh(template)

            version_message = ""
            if version_relevant_fields_changed:
                version_message = " (is_updated flag set to true - create new version when ready)"

            return workflow_pb2.UpdateWorkflowResponse(
                success=True,
                message=f"Workflow {workflow.name} updated successfully{version_message}",
            )

        except Exception as e:
            db.rollback()
            logger.error("workflow_update_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Internal server error during workflow update: {str(e)}")
            return workflow_pb2.UpdateWorkflowResponse(
                success=False, message=f"Workflow update failed: {str(e)}"
            )
        finally:
            db.close()

    def updateWorkflowSettings(
        self, request: workflow_pb2.UpdateWorkflowSettingsRequest, context: grpc.ServicerContext
    ) -> workflow_pb2.UpdateWorkflowSettingsResponse:
        db = self.get_db()
        logger.info(
            "update_workflow_settings_request",
            workflow_id=request.workflow_id,
            owner_id=request.owner.id,
        )
        try:
            workflow = db.query(Workflow).filter(Workflow.id == request.workflow_id).first()

            if not workflow:
                context.set_code(grpc.StatusCode.NOT_FOUND)
                context.set_details("Workflow not found.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Workflow not found."
                )

            if workflow.owner_id != request.owner.id:
                context.set_code(grpc.StatusCode.PERMISSION_DENIED)
                context.set_details("You are not the owner of this workflow.")
                return workflow_pb2.UpdateWorkflowSettingsResponse(
                    success=False, message="Permission denied."
                )

            updated_fields_count = 0
            if request.HasField("is_updated"):  # Check if the optional field is set
                # Allow setting is_updated field directly
                if workflow.is_updated != request.is_updated:
                    workflow.is_updated = request.is_updated
                    logger.info(f"Workflow {workflow.id} is_updated set to {request.is_updated}")
                    updated_fields_count += 1

            if request.HasField("status"):
                if workflow.status != workflow_pb2.WorkflowStatus.Name(request.status).lower():
                    workflow.status = workflow_pb2.WorkflowStatus.Name(request.status).lower()
                    logger.info(f"Workflow {workflow.id} status set to {request.status}")
                    updated_fields_count += 1

            if updated_fields_count > 0:
                db.add(workflow)
                db.commit()
                db.refresh(workflow)
                message = "Workflow settings updated successfully."
            else:
                message = "No settings were changed."

            return workflow_pb2.UpdateWorkflowSettingsResponse(success=True, message=message)
        except Exception as e:
            if "db" in locals():
                db.rollback()
            logger.error("update_workflow_settings_failed", error=str(e), exc_info=True)
            context.set_code(grpc.StatusCode.INTERNAL)
            context.set_details(f"Failed to update workflow settings: {str(e)}")
            return workflow_pb2.UpdateWorkflowSettingsResponse(
                success=False, message=f"Failed to update workflow settings: {str(e)}"
            )
        finally:
            db.close()
