{"nodes": [{"id": "LoopNode", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "LoopNode", "input_schema": {"predefined_fields": [{"field_name": "loop_input", "data_type": {"type": "string", "description": "The array of items to be processed by the loop."}, "required": true}]}, "output_schema": {"predefined_fields": [{"field_name": "current_item", "data_type": {"type": "object", "description": "", "format": "string"}}, {"field_name": "final_results", "data_type": {"type": "array", "description": "", "format": "string"}}]}}]}, {"id": "CombineTextComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "CombineTextComponent", "input_schema": {"predefined_fields": [{"field_name": "main_input", "data_type": {"type": "string", "description": "The main text or list to combine. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "num_additional_inputs", "data_type": {"type": "number", "description": "Set the number of additional text inputs to show (1-10)."}, "required": false}, {"field_name": "separator", "data_type": {"type": "string", "description": "The character or string to join the text with."}, "required": false}, {"field_name": "input_1", "data_type": {"type": "string", "description": "Text for input 1. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_2", "data_type": {"type": "string", "description": "Text for input 2. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_3", "data_type": {"type": "string", "description": "Text for input 3. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_4", "data_type": {"type": "string", "description": "Text for input 4. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_5", "data_type": {"type": "string", "description": "Text for input 5. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_6", "data_type": {"type": "string", "description": "Text for input 6. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_7", "data_type": {"type": "string", "description": "Text for input 7. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_8", "data_type": {"type": "string", "description": "Text for input 8. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_9", "data_type": {"type": "string", "description": "Text for input 9. Can be connected from another node or entered directly."}, "required": false}, {"field_name": "input_10", "data_type": {"type": "string", "description": "Text for input 10. Can be connected from another node or entered directly."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "result", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}, {"id": "DataToDataFrameComponent", "server_script_path": "", "server_tools": [{"tool_id": 1, "tool_name": "DataToDataFrameComponent", "input_schema": {"predefined_fields": [{"field_name": "input_data", "data_type": {"type": "object", "description": "The data to convert to a DataFrame. Can be connected from another node or entered directly."}, "required": true}, {"field_name": "orientation", "data_type": {"type": "string", "description": "The orientation of the input data: 'records' (list of dicts), 'columns' (dict of lists), or 'auto-detect'."}, "required": false}]}, "output_schema": {"predefined_fields": [{"field_name": "output_dataframe", "data_type": {"type": "string", "description": "", "format": "string"}}, {"field_name": "error", "data_type": {"type": "string", "description": "", "format": "string"}}]}}]}], "transitions": [{"id": "transition-LoopNode-1750756090942", "sequence": 1, "transition_type": "initial", "execution_type": "loop", "node_info": {"node_id": "LoopNode", "tools_to_use": [{"tool_id": 1, "tool_name": "LoopNode", "tool_params": {"items": [{"field_name": "loop_input", "data_type": "string", "field_value": null}]}}], "input_data": [], "output_data": [{"to_transition_id": "transition-CombineTextComponent-1750756175539", "target_node_id": "Combine Text", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "current_item", "result_path": "current_item", "edge_id": "reactflow__edge-LoopNode-1750756090942current_item-CombineTextComponent-1750756175539main_input"}]}}, {"to_transition_id": "transition-DataToDataFrameComponent-1750756229256", "target_node_id": "Data to DataFrame", "data_type": "string", "output_handle_registry": {"handle_mappings": [{"handle_id": "final_results", "result_path": "final_results", "edge_id": "reactflow__edge-LoopNode-1750756090942final_results-DataToDataFrameComponent-1750756229256input_data"}]}}]}, "result_resolution": {"node_type": "loop", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "loop_input", "handle_name": "Orders to Process", "data_type": "string", "required": true, "description": "The array of items to be processed by the loop."}], "output_handles": [{"handle_id": "current_item", "handle_name": "Current Order (Iteration Output)", "data_type": "object", "description": ""}, {"handle_id": "final_results", "handle_name": "All Results (Exit Output)", "data_type": "array", "description": ""}]}, "result_path_hints": {"current_item": "current_item", "final_results": "final_results"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.current_item", "output_data.current_item", "response.current_item", "data.current_item", "result.final_results", "output_data.final_results", "response.final_results", "data.final_results", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "current_item"}}, "approval_required": false, "end": false}, {"id": "transition-DataToDataFrameComponent-1750756229256", "sequence": 2, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "DataToDataFrameComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "DataToDataFrameComponent", "tool_params": {"items": [{"field_name": "input_data", "data_type": "object", "field_value": null}, {"field_name": "orientation", "data_type": "string", "field_value": "auto-detect"}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750756090942", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750756090942", "source_handle_id": "final_results", "target_handle_id": "input_data", "edge_id": "reactflow__edge-LoopNode-1750756090942final_results-DataToDataFrameComponent-1750756229256input_data"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "input_data", "handle_name": "Input Data", "data_type": "object", "required": true, "description": "The data to convert to a DataFrame. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "output_dataframe", "handle_name": "DataFrame", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"output_dataframe": "output_dataframe", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.output_dataframe", "output_data.output_dataframe", "response.output_dataframe", "data.output_dataframe", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "output_dataframe"}}, "approval_required": false, "end": true}, {"id": "transition-CombineTextComponent-1750756175539", "sequence": 3, "transition_type": "standard", "execution_type": "Components", "node_info": {"node_id": "CombineTextComponent", "tools_to_use": [{"tool_id": 1, "tool_name": "CombineTextComponent", "tool_params": {"items": [{"field_name": "main_input", "data_type": "string", "field_value": null}, {"field_name": "num_additional_inputs", "data_type": "number", "field_value": "1"}, {"field_name": "separator", "data_type": "string", "field_value": "\\n"}, {"field_name": "input_1", "data_type": "string", "field_value": "hello"}, {"field_name": "input_2", "data_type": "string", "field_value": ""}, {"field_name": "input_3", "data_type": "string", "field_value": ""}, {"field_name": "input_4", "data_type": "string", "field_value": ""}, {"field_name": "input_5", "data_type": "string", "field_value": ""}, {"field_name": "input_6", "data_type": "string", "field_value": ""}, {"field_name": "input_7", "data_type": "string", "field_value": ""}, {"field_name": "input_8", "data_type": "string", "field_value": ""}, {"field_name": "input_9", "data_type": "string", "field_value": ""}, {"field_name": "input_10", "data_type": "string", "field_value": ""}]}}], "input_data": [{"from_transition_id": "transition-LoopNode-1750756090942", "source_node_id": "For Each Loop", "data_type": "string", "handle_mappings": [{"source_transition_id": "transition-LoopNode-1750756090942", "source_handle_id": "current_item", "target_handle_id": "main_input", "edge_id": "reactflow__edge-LoopNode-1750756090942current_item-CombineTextComponent-1750756175539main_input"}]}], "output_data": []}, "result_resolution": {"node_type": "component", "expected_result_structure": "direct", "handle_registry": {"input_handles": [{"handle_id": "main_input", "handle_name": "Main Input", "data_type": "string", "required": false, "description": "The main text or list to combine. Can be connected from another node or entered directly."}, {"handle_id": "input_1", "handle_name": "Input 1", "data_type": "string", "required": false, "description": "Text for input 1. Can be connected from another node or entered directly."}, {"handle_id": "input_2", "handle_name": "Input 2", "data_type": "string", "required": false, "description": "Text for input 2. Can be connected from another node or entered directly."}, {"handle_id": "input_3", "handle_name": "Input 3", "data_type": "string", "required": false, "description": "Text for input 3. Can be connected from another node or entered directly."}, {"handle_id": "input_4", "handle_name": "Input 4", "data_type": "string", "required": false, "description": "Text for input 4. Can be connected from another node or entered directly."}, {"handle_id": "input_5", "handle_name": "Input 5", "data_type": "string", "required": false, "description": "Text for input 5. Can be connected from another node or entered directly."}, {"handle_id": "input_6", "handle_name": "Input 6", "data_type": "string", "required": false, "description": "Text for input 6. Can be connected from another node or entered directly."}, {"handle_id": "input_7", "handle_name": "Input 7", "data_type": "string", "required": false, "description": "Text for input 7. Can be connected from another node or entered directly."}, {"handle_id": "input_8", "handle_name": "Input 8", "data_type": "string", "required": false, "description": "Text for input 8. Can be connected from another node or entered directly."}, {"handle_id": "input_9", "handle_name": "Input 9", "data_type": "string", "required": false, "description": "Text for input 9. Can be connected from another node or entered directly."}, {"handle_id": "input_10", "handle_name": "Input 10", "data_type": "string", "required": false, "description": "Text for input 10. Can be connected from another node or entered directly."}], "output_handles": [{"handle_id": "result", "handle_name": "Combined Text", "data_type": "string", "description": ""}, {"handle_id": "error", "handle_name": "Error", "data_type": "string", "description": ""}]}, "result_path_hints": {"result": "result", "error": "error"}, "dynamic_discovery": {"enabled": false, "fallback_patterns": ["result.result", "output_data.result", "response.result", "data.result", "result.error", "output_data.error", "response.error", "data.error", "{handle_id}", "result", "output_data", "response", "data", "result.{handle_id}", "output_data.{handle_id}", "result.result", "response.data", "content", "value"], "validation_rules": [{"rule_type": "type_check", "rule_config": {"allowed_types": ["string", "number", "object", "array", "boolean"], "reject_null": false, "reject_undefined": true}}, {"rule_type": "structure_check", "rule_config": {"min_depth": 0, "max_depth": 5, "allow_nested_objects": true, "allow_arrays": true}}, {"rule_type": "content_check", "rule_config": {"min_length": 0, "reject_empty_strings": false, "reject_empty_objects": false, "reject_empty_arrays": false}}]}, "extraction_metadata": {"supports_multiple_outputs": true, "supports_nested_results": false, "requires_dynamic_discovery": false, "primary_output_handle": "result"}}, "approval_required": false, "end": true}]}