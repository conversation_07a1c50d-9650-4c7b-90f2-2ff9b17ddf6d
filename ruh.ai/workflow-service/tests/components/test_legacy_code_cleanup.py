"""
Tests for legacy code cleanup validation
"""

import pytest
import inspect
from typing import Any, Dict, List

from app.components.ai.agentic_ai import AgenticAI
from app.models.workflow_builder.components import InputBase


class TestLegacyCodeRemoval:
    """Test that legacy code has been properly removed"""

    def test_manual_tools_input_completely_removed(self):
        """Verify manual tools input is completely removed from AgenticAI"""
        component = AgenticAI()

        # Check component inputs - should not have manual 'tools' input
        input_names = [inp.name for inp in component.inputs]
        assert "tools" not in input_names, "Manual 'tools' input should be completely removed"

        # Check that workflow_components dynamic handle input exists for tools
        workflow_components_input = None
        for inp in component.inputs:
            if inp.name == "workflow_components":
                workflow_components_input = inp
                break

        assert workflow_components_input is not None, "Should have workflow_components input for tools"

    def test_legacy_dual_mode_support_removed(self):
        """Verify legacy dual-mode support code is removed"""
        component = AgenticAI()
        
        # Check that legacy dual-mode methods are removed
        legacy_methods = [
            "_process_manual_tools",
            "_merge_manual_and_workflow_tools",
            "_handle_dual_mode_tools",
            "_validate_manual_tools",
            "_convert_manual_tools_format"
        ]
        
        for method_name in legacy_methods:
            assert not hasattr(component, method_name), f"Legacy method {method_name} should be removed"

    def test_unused_imports_cleaned_up(self):
        """Verify unused imports are cleaned up"""
        # Get the AgenticAI module
        import app.components.ai.agentic_ai as agentic_ai_module
        
        # Check that certain legacy imports are not present
        legacy_imports = [
            "json",  # Should not be needed if manual tools JSON parsing is removed
            "yaml",  # Should not be needed for manual configuration
        ]
        
        module_attributes = dir(agentic_ai_module)
        
        for legacy_import in legacy_imports:
            # Check if the import is actually used in the code
            if legacy_import in module_attributes:
                # If it exists, it should be used somewhere in the code
                source_code = inspect.getsource(agentic_ai_module)
                # Allow json import if it's actually used
                if legacy_import == "json" and "json." in source_code:
                    continue
                # Allow yaml import if it's actually used
                if legacy_import == "yaml" and "yaml." in source_code:
                    continue
                # If import exists but is not used, it should be removed
                assert False, f"Unused import {legacy_import} should be removed"

    def test_no_breaking_changes_to_existing_workflows(self):
        """Verify no breaking changes to existing workflow compatibility"""
        component = AgenticAI()
        
        # Check that essential inputs still exist
        essential_inputs = [
            "query",
            "execution_type", 
            "workflow_components",
            "memory",
            "model_provider",
            "model_name"
        ]
        
        input_names = [inp.name for inp in component.inputs]
        
        for essential_input in essential_inputs:
            assert essential_input in input_names, f"Essential input {essential_input} should still exist"

    def test_workflow_components_dynamic_handle_input_present(self):
        """Verify workflow_components DynamicHandleInput is properly configured"""
        component = AgenticAI()
        
        # Find the workflow_components input
        workflow_components_input = None
        for inp in component.inputs:
            if inp.name == "workflow_components":
                workflow_components_input = inp
                break
        
        assert workflow_components_input is not None, "workflow_components input should exist"
        
        # Check that it's a DynamicHandleInput
        from app.models.workflow_builder.components import DynamicHandleInput
        assert isinstance(workflow_components_input, DynamicHandleInput), "workflow_components should be DynamicHandleInput"
        
        # Check configuration
        assert workflow_components_input.base_name == "tool", "Base name should be 'tool'"
        assert workflow_components_input.max_handles == 10, "Max handles should be 10"
        assert workflow_components_input.allow_direct_input == False, "Should not allow direct input"

    def test_legacy_build_method_deprecated(self):
        """Verify legacy build method is properly deprecated"""
        component = AgenticAI()
        
        # Check that build method exists but is deprecated
        assert hasattr(component, "build"), "Build method should still exist for backward compatibility"
        
        # Check that it returns deprecation message
        import asyncio
        result = asyncio.run(component.build())
        
        assert "deprecated" in result.get("error", "").lower(), "Build method should return deprecation message"
        assert "execute method" in result.get("error", ""), "Should mention execute method"

    def test_performance_improvements_measurable(self):
        """Test that performance improvements are measurable"""
        component = AgenticAI()
        
        # Test that component initialization is fast
        import time
        
        start_time = time.time()
        for _ in range(100):
            test_component = AgenticAI()
        end_time = time.time()
        
        initialization_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should initialize quickly (less than 100ms for 100 instances)
        assert initialization_time < 100, f"Component initialization took {initialization_time:.2f}ms for 100 instances"

    def test_tool_extraction_method_optimized(self):
        """Test that tool extraction method is optimized"""
        component = AgenticAI()
        
        # Check that _extract_connected_workflow_components method exists and is optimized
        assert hasattr(component, "_extract_connected_workflow_components"), "Tool extraction method should exist"
        
        # Test performance of tool extraction
        from app.models.workflow_builder.context import WorkflowContext
        
        # Create mock context
        context = WorkflowContext(
            workflow_id="test-workflow",
            execution_id="test-execution"
        )
        
        # Mock node inputs with multiple tools
        context.node_outputs = {
            "test-node": {
                "tool_1": {
                    "component_id": "comp-1",
                    "component_type": "TestComponent1",
                    "component_name": "Test Component 1"
                },
                "tool_2": {
                    "component_id": "comp-2", 
                    "component_type": "TestComponent2",
                    "component_name": "Test Component 2"
                }
            }
        }
        context.current_node_id = "test-node"
        
        import time
        start_time = time.time()
        
        # Extract tools multiple times to test performance
        for _ in range(100):
            tools = component._extract_connected_workflow_components(context)
        
        end_time = time.time()
        extraction_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should extract tools quickly (less than 50ms for 100 extractions)
        assert extraction_time < 50, f"Tool extraction took {extraction_time:.2f}ms for 100 extractions"

    def test_memory_usage_optimized(self):
        """Test that memory usage is optimized after cleanup"""
        import sys
        
        # Measure memory usage before creating components
        initial_size = sys.getsizeof(AgenticAI())
        
        # Create multiple components to test memory efficiency
        components = []
        for i in range(50):
            components.append(AgenticAI())
        
        # Calculate average memory per component
        total_size = sum(sys.getsizeof(comp) for comp in components)
        average_size = total_size / len(components)
        
        # Memory usage should be reasonable (less than 10KB per component)
        assert average_size < 10240, f"Average component memory usage is {average_size:.2f} bytes, should be < 10KB"

    def test_code_complexity_reduced(self):
        """Test that code complexity has been reduced"""
        # Get the source code of the AgenticAI class
        source_code = inspect.getsource(AgenticAI)
        
        # Count lines of code (excluding comments and empty lines)
        lines = source_code.split('\n')
        code_lines = [line for line in lines if line.strip() and not line.strip().startswith('#')]
        
        # AgenticAI should be reasonably sized (less than 600 lines)
        assert len(code_lines) < 600, f"AgenticAI has {len(code_lines)} lines of code, should be < 600 for maintainability"
        
        # Count number of methods
        method_count = source_code.count('def ')
        
        # Should have reasonable number of methods (less than 15)
        assert method_count < 15, f"AgenticAI has {method_count} methods, should be < 15 for maintainability"

    def test_input_definitions_streamlined(self):
        """Test that input definitions are streamlined"""
        component = AgenticAI()
        
        # Count total number of inputs
        total_inputs = len(component.inputs)
        
        # Should have reasonable number of inputs (less than 20)
        assert total_inputs < 20, f"AgenticAI has {total_inputs} inputs, should be < 20 for usability"
        
        # Check that all inputs have proper documentation
        for inp in component.inputs:
            assert hasattr(inp, 'info') and inp.info, f"Input {inp.name} should have info documentation"
            assert len(inp.info) > 10, f"Input {inp.name} info should be descriptive (> 10 chars)"

    def test_error_handling_improved(self):
        """Test that error handling has been improved"""
        component = AgenticAI()
        
        # Test that component handles missing context gracefully
        from app.models.workflow_builder.context import WorkflowContext
        
        # Create minimal context
        context = WorkflowContext(
            workflow_id="test-workflow",
            execution_id="test-execution"
        )
        
        # Test tool extraction with empty context
        tools = component._extract_connected_workflow_components(context)
        
        # Should return empty list, not crash
        assert isinstance(tools, list), "Tool extraction should return list even with empty context"
        assert len(tools) == 0, "Should return empty list when no tools connected"

    def test_documentation_updated(self):
        """Test that component documentation is updated"""
        component = AgenticAI()
        
        # Check class docstring
        assert component.__doc__ is not None, "AgenticAI should have class documentation"
        assert "AutoGen" in component.__doc__, "Documentation should mention AutoGen"
        assert "tools" in component.__doc__.lower(), "Documentation should mention tools"
        
        # Check that description is updated
        assert "tools" in component.description.lower(), "Component description should mention tools"
        assert "memory" in component.description.lower(), "Component description should mention memory"
