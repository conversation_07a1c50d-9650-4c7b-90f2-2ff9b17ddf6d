import pytest
import json
from datetime import datetime, timezone
from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.workflow import Workflow, WorkflowVersion, WorkflowMarketplaceListing
from app.services.workflow_functions import WorkflowFunctions
from app.grpc_ import workflow_pb2
from app.utils.constants.constants import WorkflowVisibilityEnum, WorkflowStatusEnum


class TestFullToggleVisibilityIntegration:
    """Test the complete user scenario: updateWorkflow → toggleVisibility multiple times"""

    @pytest.fixture
    def db_session(self):
        """Create a test database session - NO CLEANUP TO PREVENT DATA LOSS"""
        db = SessionLocal()
        try:
            yield db
        finally:
            # NO CLEANUP - Leaving test data to prevent accidental deletion of production data
            # Test data will have test-specific names/IDs that won't conflict with real data
            db.close()

    @pytest.fixture
    def workflow_service(self):
        """Create WorkflowFunctions service instance"""
        return WorkflowFunctions()

    @pytest.fixture
    def mock_context(self):
        """Mock gRPC context"""
        class MockContext:
            def __init__(self):
                self.code = None
                self.details = None
            
            def set_code(self, code):
                self.code = code
            
            def set_details(self, details):
                self.details = details
        
        return MockContext()

    def test_user_scenario_update_then_multiple_toggles(self, db_session: Session, workflow_service: WorkflowFunctions, mock_context):
        """
        Test the exact user scenario:
        1. Create workflow with name "untitled_workflow"
        2. Update workflow name to "Nikhil Test updated" and description to "test description updated"
        3. Toggle PRIVATE → PUBLIC (should show updated name/description)
        4. Toggle PUBLIC → PRIVATE
        5. Toggle PRIVATE → PUBLIC again (should still show updated name/description)
        """
        # Step 1: Create workflow with initial data
        workflow = Workflow(
            name="untitled_workflow",
            description="initial description",
            owner_id="test_user_integration_123",
            user_ids=["test_user_integration_123"],
            owner_type="user",
            visibility=WorkflowVisibilityEnum.PRIVATE,
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            tags=["test"],
            category="automation",
            is_updated=False,
            is_customizable=True,
        )
        
        db_session.add(workflow)
        db_session.flush()
        
        # Create initial version
        version = WorkflowVersion(
            workflow_id=workflow.id,
            version_number="1.0.0",
            name="untitled_workflow",
            description="initial description",
            workflow_url="https://example.com/workflow.json",
            builder_url="https://example.com/builder.json",
            start_nodes=[{"id": "start", "type": "start_node"}],
            available_nodes=[],
            category="automation",
            tags=["test"],
            changelog="Initial version",
        )
        
        db_session.add(version)
        db_session.flush()
        
        workflow.current_version_id = version.id
        db_session.commit()
        
        print(f"✅ Created workflow {workflow.id} with name '{workflow.name}'")

        # Step 2: Use actual updateWorkflow method to update name and description
        owner = workflow_pb2.Owner()
        owner.id = "test_user_integration_123"

        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = workflow.id
        update_request.name = "Nikhil Test updated"
        update_request.description = "test description updated"
        update_request.owner.CopyFrom(owner)
        
        # Set update mask to specify which fields to update
        update_request.update_mask.paths.append("name")
        update_request.update_mask.paths.append("description")

        update_response = workflow_service.updateWorkflow(update_request, mock_context)
        assert update_response.success == True

        print(f"✅ updateWorkflow successful: {update_response.message}")

        # Refresh objects from database
        db_session.refresh(workflow)
        db_session.refresh(version)

        # Verify both workflow AND current version were updated (this is the key fix)
        assert workflow.name == "Nikhil Test updated"
        assert workflow.description == "test description updated"
        assert version.name == "Nikhil Test updated"  # Version should now be updated too
        assert version.description == "test description updated"
        print(f"✅ Both workflow and version updated: '{workflow.name}' / '{version.name}'")

        # Step 3: First toggle PRIVATE → PUBLIC
        owner = workflow_pb2.Owner()
        owner.id = "test_user_integration_123"

        toggle_request = workflow_pb2.ToggleWorkflowVisibilityRequest()
        toggle_request.workflow_id = workflow.id
        toggle_request.owner.CopyFrom(owner)
        
        response1 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        
        assert response1.success == True
        assert "PUBLIC" in response1.message
        print(f"✅ First toggle successful: {response1.message}")
        
        # Check marketplace listing was created with CURRENT workflow data
        marketplace_listing = (
            db_session.query(WorkflowMarketplaceListing)
            .filter(WorkflowMarketplaceListing.workflow_id == workflow.id)
            .first()
        )
        
        assert marketplace_listing is not None
        assert marketplace_listing.title == "Nikhil Test updated"  # Should use current workflow name
        assert marketplace_listing.description == "test description updated"  # Should use current description
        assert marketplace_listing.status == WorkflowStatusEnum.ACTIVE
        listing_id = marketplace_listing.id
        
        print(f"✅ Marketplace listing created with updated data: '{marketplace_listing.title}'")

        # Step 4: Toggle PUBLIC → PRIVATE
        db_session.refresh(workflow)  # Refresh to get updated visibility
        assert workflow.visibility == WorkflowVisibilityEnum.PUBLIC
        
        response2 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        
        assert response2.success == True
        assert "PRIVATE" in response2.message
        print(f"✅ Second toggle successful: {response2.message}")
        
        # Verify marketplace listing is now INACTIVE
        db_session.refresh(marketplace_listing)
        assert marketplace_listing.status == WorkflowStatusEnum.INACTIVE
        print(f"✅ Marketplace listing deactivated")

        # Step 5: Make additional changes while PRIVATE using proper updateWorkflow method
        update_request2 = workflow_pb2.UpdateWorkflowRequest()
        update_request2.id = workflow.id
        update_request2.name = "Nikhil Test updated FINAL"
        update_request2.description = "test description updated FINAL"
        update_request2.tags.extend(["test", "final", "updated"])
        update_request2.owner.CopyFrom(owner)

        # Set update mask to specify which fields to update
        update_request2.update_mask.paths.append("name")
        update_request2.update_mask.paths.append("description")
        update_request2.update_mask.paths.append("tags")

        update_response2 = workflow_service.updateWorkflow(update_request2, mock_context)
        assert update_response2.success == True

        print(f"✅ Made additional changes using updateWorkflow: {update_response2.message}")

        # Refresh objects to get updated data
        db_session.refresh(workflow)
        db_session.refresh(version)

        # Step 6: Toggle PRIVATE → PUBLIC again (this is where the issue might be)
        db_session.refresh(workflow)  # Refresh to get updated visibility
        assert workflow.visibility == WorkflowVisibilityEnum.PRIVATE
        
        response3 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        
        assert response3.success == True
        assert "PUBLIC" in response3.message
        print(f"✅ Third toggle successful: {response3.message}")
        
        # This is the critical test: marketplace listing should be updated with LATEST changes
        db_session.refresh(marketplace_listing)
        
        # Verify the same listing was reused (not a new one created)
        all_listings = db_session.query(WorkflowMarketplaceListing).filter(
            WorkflowMarketplaceListing.workflow_id == workflow.id
        ).all()
        assert len(all_listings) == 1
        assert marketplace_listing.id == listing_id
        
        # THE KEY ASSERTION: Marketplace listing should reflect CURRENT workflow state
        assert marketplace_listing.title == "Nikhil Test updated FINAL"  # Should use latest workflow name
        assert marketplace_listing.description == "test description updated FINAL"  # Should use latest description
        assert marketplace_listing.tags == ["test", "final", "updated"]  # Should use latest tags
        assert marketplace_listing.status == WorkflowStatusEnum.ACTIVE
        assert marketplace_listing.visibility == WorkflowVisibilityEnum.PUBLIC
        
        print(f"✅ CRITICAL TEST PASSED: Marketplace listing updated with latest data: '{marketplace_listing.title}'")
        print(f"✅ Description: '{marketplace_listing.description}'")
        print(f"✅ Tags: {marketplace_listing.tags}")

    def test_user_scenario_with_actual_grpc_calls(self, db_session: Session, workflow_service: WorkflowFunctions, mock_context):
        """
        Test using actual gRPC calls to simulate the exact user workflow
        """
        # Step 1: Create workflow using createWorkflow
        owner = workflow_pb2.Owner()
        owner.id = "test_user_grpc_123"
        
        create_request = workflow_pb2.CreateWorkflowRequest()
        create_request.name = "untitled_workflow"
        create_request.description = "initial description"
        create_request.workflow_data = json.dumps({
            "nodes": [{"id": "start", "type": "start_node", "data": {"label": "Start"}}],
            "edges": []
        })
        create_request.start_nodes.append(json.dumps({"id": "start", "type": "start_node"}))
        create_request.owner.CopyFrom(owner)
        create_request.owner_type = workflow_pb2.WorkflowOwnerType.USER
        create_request.tags.append("test")
        
        create_response = workflow_service.createWorkflow(create_request, mock_context)
        assert create_response.success == True
        
        workflow_id = create_response.workflow.id
        print(f"✅ Created workflow via gRPC: {workflow_id}")

        # Step 2: Update workflow using updateWorkflow
        from google.protobuf.field_mask_pb2 import FieldMask
        
        update_request = workflow_pb2.UpdateWorkflowRequest()
        update_request.id = workflow_id
        update_request.name = "Nikhil Test updated"
        update_request.description = "test description updated"
        update_request.owner.CopyFrom(owner)
        
        # Set field mask
        update_request.update_mask.CopyFrom(FieldMask(paths=["name", "description"]))
        
        update_response = workflow_service.updateWorkflow(update_request, mock_context)
        assert update_response.success == True
        print(f"✅ Updated workflow via gRPC")

        # Step 3: Toggle visibility multiple times
        toggle_request = workflow_pb2.ToggleWorkflowVisibilityRequest()
        toggle_request.workflow_id = workflow_id
        toggle_request.owner.CopyFrom(owner)
        
        # First toggle: PRIVATE → PUBLIC
        response1 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        assert response1.success == True
        print(f"✅ First toggle: {response1.message}")
        
        # Verify marketplace listing has updated data
        marketplace_listing = (
            db_session.query(WorkflowMarketplaceListing)
            .filter(WorkflowMarketplaceListing.workflow_id == workflow_id)
            .first()
        )
        
        assert marketplace_listing is not None
        assert marketplace_listing.title == "Nikhil Test updated"
        print(f"✅ Marketplace listing has correct updated name: '{marketplace_listing.title}'")
        
        # Second toggle: PUBLIC → PRIVATE
        response2 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        assert response2.success == True
        print(f"✅ Second toggle: {response2.message}")
        
        # Third toggle: PRIVATE → PUBLIC (this should still show updated data)
        response3 = workflow_service.toggleWorkflowVisibility(toggle_request, mock_context)
        assert response3.success == True
        print(f"✅ Third toggle: {response3.message}")
        
        # Final verification
        db_session.refresh(marketplace_listing)
        assert marketplace_listing.title == "Nikhil Test updated"
        assert marketplace_listing.description == "test description updated"
        assert marketplace_listing.status == WorkflowStatusEnum.ACTIVE
        
        print(f"✅ FINAL VERIFICATION PASSED: Marketplace listing maintains updated data after multiple toggles")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])