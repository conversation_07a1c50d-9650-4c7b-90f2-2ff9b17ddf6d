"""
Tests for universal tool schema generation
"""

import pytest
from unittest.mock import Mo<PERSON>
from typing import Dict, Any, List

from app.utils.tool_schema_generator import (
    ToolSchemaGenerator,
    generate_autogen_tool_schema,
    convert_component_to_tool_schema,
    filter_component_inputs,
    MCPSchemaConverter
)
from app.models.workflow_builder.components import (
    StringInput,
    IntInput,
    BoolInput,
    DropdownInput,
    HandleInput,
    DynamicHandleInput,
    ButtonInput
)


class TestToolSchemaGenerator:
    """Test universal tool schema generation functionality"""

    def test_generate_autogen_tool_schema_basic(self):
        """Test basic AutoGen tool schema generation"""
        component_data = {
            "component_id": "data-processor-1",
            "component_type": "DataProcessor",
            "component_name": "Data Processing Tool",
            "inputs": [
                {
                    "name": "input_data",
                    "display_name": "Input Data",
                    "input_type": "string",
                    "required": True,
                    "info": "Data to be processed"
                },
                {
                    "name": "processing_mode",
                    "display_name": "Processing Mode",
                    "input_type": "dropdown",
                    "required": False,
                    "options": ["batch", "stream"],
                    "value": "batch",
                    "info": "How to process the data"
                }
            ]
        }
        
        schema = generate_autogen_tool_schema(component_data)
        
        # Verify AutoGen schema format
        assert "name" in schema
        assert "description" in schema
        assert "parameters" in schema
        
        # Verify schema structure
        assert schema["name"] == "data_processing_tool"
        assert "Data Processing Tool" in schema["description"]
        assert schema["parameters"]["type"] == "object"
        assert "properties" in schema["parameters"]
        assert "required" in schema["parameters"]
        assert schema["parameters"]["additionalProperties"] == False
        
        # Verify parameters
        properties = schema["parameters"]["properties"]
        assert "input_data" in properties
        assert "processing_mode" in properties
        
        # Verify required fields
        assert "input_data" in schema["parameters"]["required"]
        assert "processing_mode" not in schema["parameters"]["required"]
        
        # Verify parameter details
        input_data_param = properties["input_data"]
        assert input_data_param["type"] == "string"
        assert input_data_param["description"] == "Data to be processed"
        assert input_data_param["title"] == "Input Data"

    def test_filter_component_inputs_excludes_ui_elements(self):
        """Test that input filtering excludes UI-only elements"""
        inputs = [
            StringInput(name="data", display_name="Data", info="User data"),
            HandleInput(name="connection", display_name="Connection", input_types=["Any"]),
            DynamicHandleInput(name="tools", display_name="Tools", base_name="tool"),
            BoolInput(name="enable_logging", display_name="Enable Logging", value=True),
            # Button inputs should be filtered (UI-only)
            ButtonInput(name="refresh_button", display_name="Refresh", button_text="Refresh Data"),
        ]

        filtered_inputs = filter_component_inputs(inputs)

        # Should exclude HandleInput, DynamicHandleInput, and Button inputs
        assert len(filtered_inputs) == 2

        filtered_names = [inp.name for inp in filtered_inputs]
        assert "data" in filtered_names
        assert "enable_logging" in filtered_names
        assert "connection" not in filtered_names
        assert "tools" not in filtered_names
        assert "refresh_button" not in filtered_names

    def test_convert_component_to_tool_schema_performance(self):
        """Test that schema generation meets performance requirements"""
        import time
        
        # Create a large component with many inputs
        component_data = {
            "component_id": "large-component",
            "component_type": "LargeProcessor",
            "component_name": "Large Processing Component",
            "inputs": []
        }
        
        # Add 50 inputs to test performance
        for i in range(50):
            component_data["inputs"].append({
                "name": f"input_{i}",
                "display_name": f"Input {i}",
                "input_type": "string",
                "required": i % 3 == 0,  # Every 3rd input is required
                "info": f"Input parameter {i}"
            })
        
        # Measure schema generation time
        start_time = time.time()
        schema = convert_component_to_tool_schema(component_data)
        end_time = time.time()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate schema in less than 50ms
        assert generation_time < 50, f"Schema generation took {generation_time:.2f}ms, should be < 50ms"
        
        # Verify the schema was generated correctly
        assert schema["name"] == "large_processing_component"
        assert len(schema["parameters"]["properties"]) == 50
        assert len(schema["parameters"]["required"]) == 17  # Every 3rd input (0, 3, 6, ..., 48)

    def test_generate_tool_schema_error_handling(self):
        """Test error handling for malformed components"""
        # Test with missing required fields
        malformed_component = {
            "component_id": "test-component",
            # Missing component_type and component_name
            "inputs": []
        }
        
        with pytest.raises(ValueError, match="Missing required field"):
            convert_component_to_tool_schema(malformed_component)
        
        # Test with invalid input types
        invalid_input_component = {
            "component_id": "test-component",
            "component_type": "TestComponent",
            "component_name": "Test Component",
            "inputs": [
                {
                    "name": "invalid_input",
                    "display_name": "Invalid Input",
                    "input_type": "unknown_type",  # Invalid type
                    "required": True
                }
            ]
        }
        
        # Should handle gracefully and skip invalid inputs
        schema = convert_component_to_tool_schema(invalid_input_component)
        assert len(schema["parameters"]["properties"]) == 0

    def test_tool_schema_generator_class(self):
        """Test the ToolSchemaGenerator utility class"""
        generator = ToolSchemaGenerator()
        
        component_data = {
            "component_id": "text-analyzer-1",
            "component_type": "TextAnalyzer",
            "component_name": "Text Analysis Tool",
            "inputs": [
                {
                    "name": "text",
                    "display_name": "Text",
                    "input_type": "string",
                    "required": True,
                    "info": "Text to analyze"
                },
                {
                    "name": "analysis_type",
                    "display_name": "Analysis Type",
                    "input_type": "dropdown",
                    "required": False,
                    "options": ["sentiment", "keywords", "summary"],
                    "value": "sentiment",
                    "info": "Type of analysis to perform"
                }
            ]
        }
        
        schema = generator.generate_schema(component_data)
        
        # Verify schema generation
        assert schema["name"] == "text_analysis_tool"
        assert "Text Analysis Tool" in schema["description"]
        assert len(schema["parameters"]["properties"]) == 2
        assert "text" in schema["parameters"]["required"]
        assert "analysis_type" not in schema["parameters"]["required"]


class TestMCPSchemaConverter:
    """Test MCP component schema conversion"""

    def test_mcp_component_schema_conversion(self):
        """Test MCP marketplace component schema conversion"""
        mcp_component = {
            "component_id": "mcp-translate-1",
            "component_type": "MCPMarketplace",
            "component_name": "Translation Tool",
            "mcp_metadata": {
                "server_url": "http://localhost:8000",
                "tool_schema": {
                    "name": "translate",
                    "description": "Translate text between languages",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "text": {
                                "type": "string",
                                "description": "Text to translate"
                            },
                            "target_language": {
                                "type": "string",
                                "description": "Target language code"
                            }
                        },
                        "required": ["text", "target_language"]
                    }
                }
            }
        }
        
        converter = MCPSchemaConverter()
        schema = converter.convert_mcp_to_autogen_schema(mcp_component)
        
        # Verify MCP schema conversion
        assert schema["name"] == "translate"
        assert schema["description"] == "Translate text between languages"
        assert schema["parameters"]["type"] == "object"
        assert "text" in schema["parameters"]["properties"]
        assert "target_language" in schema["parameters"]["properties"]
        assert schema["parameters"]["required"] == ["text", "target_language"]

    def test_mcp_schema_converter_error_handling(self):
        """Test MCP schema converter error handling"""
        converter = MCPSchemaConverter()
        
        # Test with missing MCP metadata
        invalid_mcp_component = {
            "component_id": "invalid-mcp",
            "component_type": "MCPMarketplace",
            "component_name": "Invalid MCP Component"
            # Missing mcp_metadata
        }
        
        with pytest.raises(ValueError, match="Missing MCP metadata"):
            converter.convert_mcp_to_autogen_schema(invalid_mcp_component)
        
        # Test with malformed tool schema
        malformed_schema_component = {
            "component_id": "malformed-mcp",
            "component_type": "MCPMarketplace",
            "component_name": "Malformed MCP Component",
            "mcp_metadata": {
                "server_url": "http://localhost:8000",
                "tool_schema": {
                    "name": "malformed_tool"
                    # Missing description and parameters
                }
            }
        }
        
        # Should handle gracefully and provide defaults
        schema = converter.convert_mcp_to_autogen_schema(malformed_schema_component)
        assert schema["name"] == "malformed_tool"
        assert "description" in schema
        assert "parameters" in schema


class TestUniversalSchemaGeneration:
    """Test universal schema generation for both regular and MCP components"""

    def test_universal_schema_generation_regular_component(self):
        """Test universal schema generation for regular components"""
        regular_component = {
            "component_id": "api-caller-1",
            "component_type": "APICaller",
            "component_name": "API Calling Tool",
            "inputs": [
                {
                    "name": "url",
                    "display_name": "URL",
                    "input_type": "string",
                    "required": True,
                    "info": "API endpoint URL"
                },
                {
                    "name": "method",
                    "display_name": "HTTP Method",
                    "input_type": "dropdown",
                    "required": False,
                    "options": ["GET", "POST", "PUT", "DELETE"],
                    "value": "GET",
                    "info": "HTTP method to use"
                }
            ]
        }
        
        generator = ToolSchemaGenerator()
        schema = generator.generate_universal_schema(regular_component)
        
        # Should generate regular component schema
        assert schema["name"] == "api_calling_tool"
        assert len(schema["parameters"]["properties"]) == 2
        assert "url" in schema["parameters"]["required"]

    def test_universal_schema_generation_mcp_component(self):
        """Test universal schema generation for MCP components"""
        mcp_component = {
            "component_id": "mcp-weather-1",
            "component_type": "MCPMarketplace",
            "component_name": "Weather Tool",
            "mcp_metadata": {
                "server_url": "http://weather-api:8000",
                "tool_schema": {
                    "name": "get_weather",
                    "description": "Get current weather for a location",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "location": {
                                "type": "string",
                                "description": "City name or coordinates"
                            }
                        },
                        "required": ["location"]
                    }
                }
            }
        }
        
        generator = ToolSchemaGenerator()
        schema = generator.generate_universal_schema(mcp_component)
        
        # Should generate MCP component schema
        assert schema["name"] == "get_weather"
        assert schema["description"] == "Get current weather for a location"
        assert "location" in schema["parameters"]["properties"]
        assert schema["parameters"]["required"] == ["location"]

    def test_batch_schema_generation_performance(self):
        """Test batch schema generation performance"""
        import time
        
        generator = ToolSchemaGenerator()
        
        # Create 10 components for batch processing
        components = []
        for i in range(10):
            components.append({
                "component_id": f"component-{i}",
                "component_type": "TestComponent",
                "component_name": f"Test Component {i}",
                "inputs": [
                    {
                        "name": f"input_{j}",
                        "display_name": f"Input {j}",
                        "input_type": "string",
                        "required": j == 0,
                        "info": f"Input parameter {j}"
                    }
                    for j in range(5)  # 5 inputs per component
                ]
            })
        
        # Measure batch generation time
        start_time = time.time()
        schemas = generator.generate_batch_schemas(components)
        end_time = time.time()
        
        generation_time = (end_time - start_time) * 1000  # Convert to milliseconds
        
        # Should generate all schemas in less than 100ms
        assert generation_time < 100, f"Batch generation took {generation_time:.2f}ms, should be < 100ms"
        
        # Verify all schemas were generated
        assert len(schemas) == 10
        for i, schema in enumerate(schemas):
            assert schema["name"] == f"test_component_{i}"
            assert len(schema["parameters"]["properties"]) == 5
